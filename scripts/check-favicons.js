#!/usr/bin/env node

/**
 * Favicon Checker Script
 * This script helps verify that all favicon files exist and are properly configured
 */

import fs from 'fs';
import path from 'path';

const faviconFiles = [
    'public/favicons/favicon.ico',
    'public/favicons/favicon-16x16.png',
    'public/favicons/favicon-32x32.png',
    'public/favicons/apple-touch-icon.png',
    'public/favicons/android-chrome-192x192.png',
    'public/favicons/android-chrome-512x512.png',
    'public/favicons/mstile-150x150.png',
    'public/favicons/safari-pinned-tab.svg',
    'public/favicons/site.webmanifest',
    'public/favicons/browserconfig.xml'
];

console.log('🔍 Checking favicon files...\n');

let allFilesExist = true;

faviconFiles.forEach(file => {
    if (fs.existsSync(file)) {
        const stats = fs.statSync(file);
        const size = (stats.size / 1024).toFixed(2);
        console.log(`✅ ${file} (${size} KB)`);
    } else {
        console.log(`❌ ${file} - FILE MISSING`);
        allFilesExist = false;
    }
});

console.log('\n📋 Favicon Configuration Summary:');
console.log('================================');

if (allFilesExist) {
    console.log('✅ All favicon files are present');
    console.log('✅ Cache-busting parameters added (?v=2025)');
    console.log('✅ Web manifest updated with personal branding');
    console.log('✅ Browser config updated');
    
    console.log('\n🚀 Next Steps:');
    console.log('1. Deploy your changes to production');
    console.log('2. Clear your browser cache (Ctrl+F5 or Cmd+Shift+R)');
    console.log('3. Test in incognito/private mode');
    console.log('4. Test in different browsers');
    console.log('5. Check mobile devices');
    
    console.log('\n💡 If favicon still doesn\'t update:');
    console.log('- Wait 24-48 hours for DNS/CDN cache to clear');
    console.log('- Use browser dev tools to check if favicon requests return 200 status');
    console.log('- Verify favicon files are actually your new personal ones');
} else {
    console.log('❌ Some favicon files are missing');
    console.log('Please ensure all favicon files are uploaded to public/favicons/');
}

console.log('\n🔗 Favicon URLs that should work:');
console.log('- https://your-domain.com/favicons/favicon.ico');
console.log('- https://your-domain.com/favicons/apple-touch-icon.png');
console.log('- https://your-domain.com/favicons/android-chrome-192x192.png');
