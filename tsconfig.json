{"extends": "astro/tsconfigs/base", "compilerOptions": {"strictNullChecks": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@config/*": ["src/config/*"], "@js/*": ["src/js/*"], "@layouts/*": ["src/layouts/*"], "@components/*": ["src/components/*"], "@assets/*": ["src/assets/*"], "@images/*": ["src/assets/images/*"], "@videos/*": ["src/assets/videos/*"], "@/*": ["src/*"]}, "jsxImportSource": "react", "moduleResolution": "bundler"}}