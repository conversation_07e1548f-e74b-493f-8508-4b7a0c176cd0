/* ⚡ NON-CRITICAL CSS - Loaded asynchronously after initial render */

/* Advanced form animations and interactions */
.form__input:hover {
	border-color: rgb(156 163 175);
	transition: border-color 0.2s ease;
}

.form__input:focus {
	transform: translateY(-1px);
	box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Enhanced Calendly widget styles */
.calendly-inline-widget iframe {
	overflow: hidden !important;
	border-radius: 0.5rem;
}

.calendly-inline-widget[data-auto-expand="true"] {
	height: auto !important;
	min-height: inherit;
}

/* Advanced loading animations */
@keyframes pulse-soft {
	0%, 100% { opacity: 1; }
	50% { opacity: 0.7; }
}

.loading-pulse {
	animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced mobile interactions */
@media (max-width: 1023px) {
	.form__input:focus {
		font-size: 16px; /* Prevent zoom on iOS */
	}
	
	.booking-grid {
		gap: 1.5rem !important;
	}
}

/* Dark mode support (when enabled) */
@media (prefers-color-scheme: dark) {
	.form__input {
		background-color: rgb(31 41 55);
		border-color: rgb(75 85 99);
		color: rgb(243 244 246);
	}
	
	.form__label {
		color: rgb(209 213 219);
	}
	
	#calendly-container {
		border-color: rgb(75 85 99);
	}
}

/* Advanced accessibility features */
.form__input:focus-visible {
	outline: 2px solid rgb(59 130 246);
	outline-offset: 2px;
}

/* Smooth transitions for better UX */
.transition-all {
	transition: all 0.3s ease;
}

/* Enhanced error states */
.form__input.error {
	border-color: rgb(239 68 68);
	box-shadow: 0 0 0 1px rgb(239 68 68);
}

.form__input.error:focus {
	border-color: rgb(220 38 38);
	box-shadow: 0 0 0 1px rgb(220 38 38);
}

/* Success states */
.form__input.success {
	border-color: rgb(34 197 94);
	box-shadow: 0 0 0 1px rgb(34 197 94);
}

/* Advanced Calendly customizations */
.calendly-inline-widget {
	box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Responsive enhancements */
@media (min-width: 1024px) {
	.booking-grid {
		gap: 2rem;
	}
	
	.form__input {
		font-size: 1rem;
		padding: 0.75rem 1rem;
	}
}

/* Print styles */
@media print {
	.calendly-inline-widget {
		display: none;
	}
	
	.form__input {
		border: 1px solid #000;
		background: white;
	}
}
