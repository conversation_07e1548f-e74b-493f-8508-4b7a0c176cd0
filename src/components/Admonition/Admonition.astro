---
import { Icon } from "astro-icon/components";

interface Props {
	variant: "tip" | "caution" | "danger" | "info";
}

const { variant } = Astro.props as Props;

// create a switch statement that switches on "variant" and returns the appropriate icon
const icon = (() => {
	switch (variant) {
		case "tip":
			return "tabler/bulb";
		case "caution":
			return "tabler/alert-triangle";
		case "danger":
			return "tabler/flame";
		case "info":
			return "tabler/info-circle";
	}
})();

// Map variant to Italian names
const getItalianLabel = (variant: string) => {
	switch (variant) {
		case "tip":
			return "SUGGERIMENTO";
		case "caution":
			return "ATTENZIONE";
		case "danger":
			return "PERICOLO";
		case "info":
			return "INFO";
		default:
			return variant.toUpperCase();
	}
};
---

<div
	class:list={[
		"admonition text-base-800 dark:text-base-200 my-3 rounded-lg border-l-4 px-4 py-3",
		{
			"border-success/50 text-success-foreground bg-success/10 admonition-success":
				variant === "tip",
		},
		{
			"border-warning/50 text-foreground bg-warning/10 admonition-warning": variant === "caution",
		},
		{
			"border-error/50 text-foreground bg-error/10 admonition-error": variant === "danger",
		},
		{
			"border-info/50 text-info-foreground bg-info/10 admonition-info": variant === "info",
		},
	]}
>
	<div class="not-content text-base-700 flex items-center gap-2 pb-2">
		<Icon name={icon} class="dark:text-base-300 size-7" aria-hidden="true" />
		<p class="dark:text-base-300 text-sm font-bold">
			{getItalianLabel(variant)}
		</p>
	</div>
	<slot />
</div>

<style is:global>
	@import "tailwindcss/theme" theme(reference);
	@import "../../styles/tailwind-theme.css" theme(reference);

	.admonition > :first-child {
		@apply mt-0;
	}
	.admonition > :nth-child(2) {
		@apply mt-0;
	}
	.admonition > :last-child {
		@apply mb-0;
	}
	.admonition-success code {
		@apply bg-success/20 border-none px-0.5;
	}
	.admonition-info code {
		@apply bg-info/20 border-none px-0.5;
	}
	.admonition-warning code {
		@apply bg-warning/20 border-none px-0.5;
	}
	.admonition-error code {
		@apply bg-error/20 border-none px-0.5;
	}
</style>