---
import { getImage } from "astro:assets";
import { type CollectionEntry } from "astro:content";
import { SEO } from "astro-seo";

// data
import siteData from "@config/siteData.json.ts";
import { type LocationData, getPrimaryLocation, getLocationSeoData } from "@config/locations.ts";

// component import
import jsonLDGenerator from "@js/jsonLD.ts";

interface BaseProps {
	title: string;
	description: string;
	noindex?: boolean;
	primaryLocation?: LocationData; // New optional location prop
}

interface GenProps extends BaseProps {
	type: "general";
	image?: ImageMetadata;
}

interface BlogProps extends BaseProps {
	type: "blog";
	image: ImageMetadata;
	authors: CollectionEntry<"authors">[];
	postFrontmatter: CollectionEntry<"blog">["data"];
}

interface CategoryProps extends BaseProps {
	type: "category";
	category: string;
	postCount: number;
	image?: ImageMetadata;
}

const { type, title, description, image, noindex = false, primaryLocation } = Astro.props as GenProps | BlogProps | CategoryProps;

// Location handling: use provided location or default to primary
const activeLocation = primaryLocation || getPrimaryLocation();
const locationSeoData = getLocationSeoData(activeLocation);

let optimizedImage: Awaited<ReturnType<typeof getImage>> | undefined;
let jsonLD: string;
const canonicalUrl = new URL(Astro.url.pathname, Astro.site);

// if this is a blog page we do things differently
if (type === "blog") {
	const { authors, postFrontmatter } = Astro.props as BlogProps;

	optimizedImage = await getImage({
		src: image!,
		format: "webp",
		quality: "high",
		width: 1200,
		height: 630, // Standard OG image ratio 1200x630
	});

	jsonLD = jsonLDGenerator({
		type: "blog",
		postFrontmatter: postFrontmatter,
		image: optimizedImage,
		authors: authors,
		canonicalUrl: canonicalUrl,
		location: activeLocation,
	});
}
// if this is a category page
else if (type === "category") {
	const { category, postCount } = Astro.props as CategoryProps;

	// Ottimizza immagine se fornita, altrimenti usa default
	if (image) {
		optimizedImage = await getImage({
			src: image,
			format: "webp",
			quality: "high",
			width: 1200,
			height: 630, // Standard OG image ratio 1200x630
		});
	}

	jsonLD = jsonLDGenerator({
		type: "category",
		category: category,
		postCount: postCount,
		canonicalUrl: canonicalUrl,
		location: activeLocation,
	});
}
// for general pages
else {
	jsonLD = jsonLDGenerator({
		type: "general",
		location: activeLocation,
	});
}

function trimEndSlash(str: string) {
	if (str[str.length - 1] === "/") {
		return str.slice(0, -1);
	} else {
		return str;
	}
}

// put together the image URL
const imageUrl =
	trimEndSlash(Astro.site?.toString() || "") + (optimizedImage?.src || siteData.defaultImage.src);
---

<SEO
	title={title}
	description={description}
	canonical={canonicalUrl.toString()}
	openGraph={{
		basic: {
			title: title,
			type: type === "blog" ? "article" : "website",
			image: imageUrl,
			url: canonicalUrl.toString(),
		},
		optional: {
			description: description,
			siteName: siteData.name,
			locale: "it_IT",
		},
		image: {
			type: "image/webp",
			width: optimizedImage?.attributes.width || 1200,
			height: optimizedImage?.attributes.height || 630,
			alt: title,
		},
		article: type === "blog" ? {
			authors: [(Astro.props as BlogProps).authors?.[0]?.data.name].filter(Boolean),
			publishedTime: (Astro.props as BlogProps).postFrontmatter?.pubDate?.toISOString(),
			modifiedTime: (Astro.props as BlogProps).postFrontmatter?.updatedDate?.toISOString(),
			section: "Nutrizione e Benessere",
			tags: (Astro.props as BlogProps).postFrontmatter?.categories,
		} : undefined,
	}}
	twitter={{
		site: "@" + siteData.author.twitter,
		creator: "@" + siteData.author.twitter,
		card: "summary_large_image",
		title: title,
		description: description,
		image: imageUrl,
	}}
	noindex={noindex}
	nofollow={noindex}
/>

<!-- Italian locale and location-specific meta tags -->
<meta name="language" content="it" />
<meta name="geo.region" content={locationSeoData.geoRegion} />
<meta name="geo.placename" content={locationSeoData.geoPlacename} />
<meta name="geo.position" content={locationSeoData.geoPosition} />
<meta name="ICBM" content={locationSeoData.icbm} />

<!-- Performance optimization: preload critical images -->
{optimizedImage && (
	<link rel="preload" as="image" href={optimizedImage.src} />
)}

<!-- Accessibility and SEO improvements -->
<meta name="robots" content={noindex ? "noindex, nofollow" : "index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"} />
<meta name="googlebot" content={noindex ? "noindex, nofollow" : "index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"} />

{type === "blog" && (
	<>
		<meta name="article:author" content={(Astro.props as BlogProps).authors?.[0]?.data.name} />
		<meta name="article:section" content="Nutrizione e Benessere" />
		{(Astro.props as BlogProps).postFrontmatter?.categories?.map((category: string) => (
			<meta name="article:tag" content={category} />
		))}
	</>
)}

<!-- hreflang attributes removed - single language site -->

<!-- JSON LD -->
<Fragment set:html={jsonLD} />