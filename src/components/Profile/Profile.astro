---
import { Image } from "astro:assets";
import { getEntry, render } from "astro:content";
import ExternalLink from "@components/MarkdownComponents/ExternalLink.astro";

import { type teamMember } from "@config/types/configDataTypes";

const { image, name, title, bio, seo, useMdx = false } = Astro.props as teamMember & { useMdx?: boolean };

// Get biography content from otherPages collection
let BiographyContent: any = null;
if (useMdx) {
	try {
		const biographyEntry = await getEntry("otherPages", "it/biografia");
		if (biographyEntry) {
			const { Content } = await render(biographyEntry);
			BiographyContent = Content;
		} else {
			console.log("Biography entry not found with ID: it/biografia");
		}
	} catch (error) {
		console.error("Error loading biography:", error);
	}
}
---

<div class="w-full">
	<!-- SEO structured data -->
	{seo && (
		<script type="application/ld+json" set:html={JSON.stringify({
			"@context": "https://schema.org",
			"@type": "Person",
			"name": name,
			"jobTitle": title,
			"description": seo.description,
			"address": {
				"@type": "PostalAddress",
				"addressLocality": seo.location
			},
			"knowsAbout": seo.specializations,
			"image": image.src
		})} />
	)}

	<div class="mx-auto max-w-4xl py-10 md:py-12">
		<!-- Mobile layout (default) - centered header -->
		<div class="md:hidden text-center mb-12">
			<Image
				src={image}
				alt={name}
				height={320}
				class="mx-auto mb-6 aspect-square size-64 rounded-full object-cover"
			/>

			<!-- Dott. Emanuele Belloni Biologo Nutrizionista e Chinesiologo. Sia per web che mobile responsive. -->
			<h3 class="h2 mb-2 font-semibold whitespace-nowrap text-2xl sm:text-3xl">{name}</h3>
			<span class="text-primary-500 mb-6 inline-block text-base sm:text-xl font-medium whitespace-nowrap">{title}</span>
		</div>

		<!-- Desktop layout (md and up) - side by side -->
		<div class="hidden md:flex md:items-center md:gap-12 md:mb-12">
			<!-- Photo on the left - larger for desktop -->
			<div class="md:flex-shrink-0">
				<Image
					src={image}
					alt={name}
					height={400}
					class="aspect-square md:size-80 rounded-full object-cover"
				/>
			</div>

			<!-- Text on the right, vertically centered - larger typography -->
			<div class="md:flex-1">
				<h3 class="h2 mb-3 font-semibold whitespace-nowrap md:text-4xl">{name}</h3>
				<span class="text-primary-500 inline-block md:text-2xl font-medium whitespace-nowrap">{title}</span>
			</div>
		</div>

		<!-- Bio section - left aligned -->
		<div class="text-left max-w-none">
			{useMdx && BiographyContent ? (
				<div class="text-base-content text-base markdown-content max-w-none">
					<BiographyContent components={{ a: ExternalLink }} />
				</div>
			) : (
				<p class="description text-base leading-relaxed">{bio}</p>
			)}
		</div>
	</div>
</div>