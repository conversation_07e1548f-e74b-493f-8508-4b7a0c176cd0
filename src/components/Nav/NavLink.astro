---
import { Icon } from "astro-icon/components";



// data
import { type navLinkItem } from "@config/types/configDataTypes";

/**
 * Compares two paths ensuring they both have trailing slashes for consistency
 * @param path1 First path to compare
 * @param path2 Second path to compare
 * @returns boolean indicating if the paths are equal
 */
const comparePathsWithTrailingSlash = (path1: string, path2: string): boolean => {
	const ensureTrailingSlash = (path: string): string => (path.endsWith("/") ? path : `${path}/`);

	return ensureTrailingSlash(path1) === ensureTrailingSlash(path2);
};

interface Props {
	navItem: navLinkItem;
	class?: string;
	noIcon?: boolean;
}

const { navItem, class: className, noIcon = false } = Astro.props as Props;
const { text, link, newTab = false, icon } = navItem;



const domain = import.meta.env.SITE; // pulls from astro.config.mjs
let actualLink: string;
if (!link.includes(domain) && !link.startsWith("/") && !link.startsWith("#")) {
	// it is an external link
	actualLink = link;
} else {
	// internal link, so we need to add the locale to the link
	actualLink = link;
}
// remove trailing slash so IDs will work
if (actualLink.endsWith("/") && actualLink !== "/") {
	actualLink = actualLink.slice(0, -1);
}

const currentPath = comparePathsWithTrailingSlash(Astro.url.pathname, actualLink);
---

<li>
	<a
		class:list={[
			`primary-focus nav__link--base relative flex min-h-10 w-full items-center gap-1.5 rounded-md px-4 py-3 leading-tight ${className}`,
			{
				// styling for current active page link, if desired
				"": currentPath,
			},
		]}
		href={actualLink}
		target={newTab ? "_blank" : ""}
		rel={newTab ? "noopener noreferrer" : ""}
		aria-label={text}
	>
		{
			icon && !noIcon && (
				<Icon name={icon} aria-hidden="true" class="nav-icon mt-1 size-5 self-start" />
			)
		}
		{text}
	</a>
</li>

<style>
	/* Custom 848px breakpoint for icon size */
	@media (min-width: 848px) {
		.nav-icon {
			width: 1rem;
			height: 1rem;
		}
	}
</style>