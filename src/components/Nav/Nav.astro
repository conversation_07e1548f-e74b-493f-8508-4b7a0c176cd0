---
// components
import NavLink from "@components/Nav/NavLink.astro";
import NavDropdownToggle from "./NavDropdown/NavDropdownToggle.astro";
import MegaMenuDropdownToggle from "@components/Nav/NavDropdown/MegaMenuDropdownToggle.astro";
import MobileNav from "@components/Nav/MobileNav/MobileNav.astro";
import SiteLogo from "@components/SiteLogo/SiteLogo.astro";
import Button from "@components/Button/Button.astro";

// DARK MODE TEMPORARILY DISABLED - To re-enable, uncomment the line below
// import ThemeToggle from "@components/ThemeToggle/ThemeToggle.astro";

// data
import navData from "@config/navData.json.ts";
// console.log(JSON.stringify(navData, null, 2));
---

<div
	id="nav__container"
	class="navbar--initial fixed top-0 left-0 z-30 flex w-full flex-col transition-all duration-300"
	transition:name={`nav`}
>
	<!-- Desktop Navbar -->
	<div
		id="nav__desktop-wrapper"
		class="nav-desktop-wrapper mx-auto hidden w-[95%] transition-all duration-500 ease-out"
	>
		<div
			id="nav__desktop-content"
			class="site-container flex h-14 w-full items-center rounded-full transition-all duration-500 ease-out"
		>
			<header class="relative flex w-full items-center gap-2 px-4">
				<!-- home button / image -->
				<div class="flex flex-auto justify-start">
					<SiteLogo />
				</div>

				<!-- desktop nav menu -->
				<div class="flex flex-auto justify-center">
					<nav class="block">
						<ul class="flex h-fit items-center px-4">
							{
								// if dropdown exists, setup the dropdown, otherwise it is just a link
								navData.map((navItem) =>
									"dropdown" in navItem ? (
										// non-mobile dropdown menu
										<>
											{/* <NavDropdown navItem={navItem} /> */}
											<NavDropdownToggle navItem={navItem} />
										</>
									) : "megaMenuColumns" in navItem ? (
										<MegaMenuDropdownToggle {navItem} />
									) : (
										// normal nav link
										<NavLink {navItem} />
									),
								)
							}
						</ul>
					</nav>
				</div>

				<div class="flex flex-auto justify-end">
					<!-- DARK MODE TEMPORARILY DISABLED - To re-enable, uncomment the line below -->
					<!-- <ThemeToggle class="mr-3 -ml-4 block" /> -->

					<Button
						variant="primary"
						class="my-auto block px-4 py-1.5 font-medium"
						href="/contatti-e-prenotazioni"
					>
						Prenota Ora
					</Button>
				</div>
			</header>
		</div>
	</div>

	<!-- Mobile Navbar -->
	<div
		id="nav__mobile-wrapper"
		class="nav-mobile-wrapper mx-auto flex w-full transition-all duration-500 ease-out"
	>
		<div
			id="nav__mobile-content"
			class="flex h-14 w-full items-center rounded-full transition-all duration-500 ease-out px-4 mx-4"
		>
			<header class="relative flex w-full items-center gap-2">
				<!-- home button / image -->
				<div class="flex flex-auto justify-start">
					<SiteLogo />
				</div>

				<!-- mobile nav menu -->
				<div class="flex flex-auto justify-end">
					<MobileNav />
				</div>
			</header>
		</div>
	</div>
</div>

<style>
	@import "tailwindcss/theme" theme(reference);
	@import "../../styles/tailwind-theme.css" theme(reference);

	.navbar--scrolled {
		/* Remove border to prevent line visibility during contraction */
	}

	.navbar--initial {
		@apply py-2;
		/* Remove border-b-transparent to prevent line visibility */
	}

	/* Desktop navbar styles */
	.navbar--desktop-scrolled {
		background: rgba(248, 247, 244, 0.97);
		backdrop-filter: blur(12px);
		-webkit-backdrop-filter: blur(12px);
		box-shadow:
			0 0 24px rgba(0, 106, 113, 0.12),
			0 1px 1px rgba(0, 0, 0, 0.08),
			0 0 0 1px rgba(0, 106, 113, 0.08),
			0 0 4px rgba(0, 106, 113, 0.15),
			0 16px 68px rgba(0, 106, 113, 0.08),
			0 1px 0 rgba(255, 255, 255, 0.15) inset;
		border: none; /* Remove border to prevent line visibility */
	}

	.navbar--desktop-initial {
		background: transparent;
		backdrop-filter: none;
		-webkit-backdrop-filter: none;
		box-shadow: none;
		border: none;
	}

	/* Mobile navbar styles */
	.navbar--mobile-scrolled {
		background: rgba(248, 247, 244, 0.97);
		backdrop-filter: blur(12px);
		-webkit-backdrop-filter: blur(12px);
		box-shadow:
			0 0 24px rgba(0, 106, 113, 0.12),
			0 1px 1px rgba(0, 0, 0, 0.08),
			0 0 0 1px rgba(0, 106, 113, 0.08),
			0 0 4px rgba(0, 106, 113, 0.15),
			0 16px 68px rgba(0, 106, 113, 0.08),
			0 1px 0 rgba(255, 255, 255, 0.15) inset;
		border: none; /* Remove border to prevent line visibility */
	}

	.navbar--mobile-initial {
		background: transparent;
		backdrop-filter: none;
		-webkit-backdrop-filter: none;
		box-shadow: none;
		border: none;
	}

	/* Dark mode support */
	.dark .navbar--desktop-scrolled,
	.dark .navbar--mobile-scrolled {
		background: rgba(37, 39, 42, 0.97);
		border: none;
		box-shadow:
			0 0 24px rgba(0, 106, 113, 0.15),
			0 1px 1px rgba(0, 0, 0, 0.15),
			0 0 0 1px rgba(0, 106, 113, 0.12),
			0 0 4px rgba(0, 106, 113, 0.18),
			0 16px 68px rgba(0, 106, 113, 0.1),
			0 1px 0 rgba(255, 255, 255, 0.08) inset;
	}

	/* Performance optimizations */
	#nav__desktop-wrapper,
	#nav__mobile-wrapper {
		will-change: transform, width;
		transform: translateZ(0); /* Force hardware acceleration */
	}

	#nav__desktop-content,
	#nav__mobile-content {
		will-change: background, backdrop-filter, box-shadow, border, padding;
		transition: background 0.4s cubic-bezier(0.4, 0, 0.2, 1),
					backdrop-filter 0.4s cubic-bezier(0.4, 0, 0.2, 1),
					box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1),
					border 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		transform: translateZ(0); /* Force hardware acceleration */
	}

	/* Ensure navbar stays above other content */
	#nav__container {
		isolation: isolate;
	}

	/* Custom 848px breakpoint for navbar */
	.nav-desktop-wrapper {
		display: none;
	}

	.nav-mobile-wrapper {
		display: flex;
		padding-top: 1rem; /* 16px padding sopra in mobile mode */
	}

	@media (min-width: 848px) {
		.nav-desktop-wrapper {
			display: flex;
		}

		.nav-mobile-wrapper {
			display: none;
			padding-top: 0; /* Rimuovi padding in desktop mode */
		}
	}

	/* Smooth hover effects for navigation items */
	#nav__desktop-content .nav__link--base,
	#nav__mobile-content .nav__link--base {
		transition: color 0.2s ease, background-color 0.2s ease;
	}

	/* Reduce motion for users who prefer it */
	@media (prefers-reduced-motion: reduce) {
		#nav__desktop-wrapper,
		#nav__mobile-wrapper,
		#nav__desktop-content,
		#nav__mobile-content {
			transition: none !important;
		}
	}
</style>

<script>
	import anime from "animejs/lib/anime.es.js";

	let navbar: HTMLElement | null;
	let desktopWrapper: HTMLElement | null;
	let desktopContent: HTMLElement | null;
	let mobileWrapper: HTMLElement | null;
	let mobileContent: HTMLElement | null;
	let isScrolled = false;
	let prefersReducedMotion = false;

	// Check for reduced motion preference
	function checkReducedMotion() {
		prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
	}

	let currentAnimation: any = null;

	function animateNavbar(scrolled: boolean) {
		if (scrolled === isScrolled) return;
		isScrolled = scrolled;

		// Cancel any existing animation to prevent conflicts
		if (currentAnimation) {
			currentAnimation.pause();
			currentAnimation = null;
		}

		// Desktop animations
		if (desktopWrapper && desktopContent) {
			// Skip animations if user prefers reduced motion
			if (prefersReducedMotion) {
				// Apply final styles immediately - keep width at 95% always
				desktopWrapper.style.width = '95%';
				desktopWrapper.style.transform = scrolled ? 'translateY(20px)' : 'translateY(0px)';
				desktopContent.style.paddingLeft = scrolled ? '16px' : '0px';
				desktopContent.style.paddingRight = scrolled ? '16px' : '0px';

				// Apply classes after styles
				if (scrolled) {
					desktopContent.classList.add('navbar--desktop-scrolled');
					desktopContent.classList.remove('navbar--desktop-initial');
				} else {
					desktopContent.classList.add('navbar--desktop-initial');
					desktopContent.classList.remove('navbar--desktop-scrolled');
				}
			} else {
				// Create timeline for synchronized animations
				const timeline = anime.timeline({
					duration: 500,
					easing: 'easeOutCubic',
					complete: () => {
						// Apply final classes after animation completes
						if (scrolled) {
							desktopContent?.classList.add('navbar--desktop-scrolled');
							desktopContent?.classList.remove('navbar--desktop-initial');
						} else {
							desktopContent?.classList.add('navbar--desktop-initial');
							desktopContent?.classList.remove('navbar--desktop-scrolled');
						}
						currentAnimation = null;
					}
				});

				// Add all animations to timeline for perfect synchronization - keep width at 95% always
				timeline
					.add({
						targets: desktopWrapper,
						width: '95%',
						translateY: scrolled ? 20 : 0,
					})
					.add({
						targets: desktopContent,
						paddingLeft: scrolled ? '16px' : '0px',
						paddingRight: scrolled ? '16px' : '0px',
					}, 0); // Start at the same time (offset 0)

				currentAnimation = timeline;

				// Apply visual classes immediately for CSS transitions
				if (scrolled) {
					desktopContent.classList.add('navbar--desktop-scrolled');
					desktopContent.classList.remove('navbar--desktop-initial');
				} else {
					desktopContent.classList.add('navbar--desktop-initial');
					desktopContent.classList.remove('navbar--desktop-scrolled');
				}
			}
		}

		// Mobile animations
		if (mobileWrapper && mobileContent) {
			// Skip animations if user prefers reduced motion
			if (prefersReducedMotion) {
				// Apply final styles immediately - keep width at 100% always
				mobileWrapper.style.width = '100%';
				mobileWrapper.style.transform = scrolled ? 'translateY(24px)' : 'translateY(0px)';
				mobileContent.style.paddingLeft = scrolled ? '12px' : '16px';
				mobileContent.style.paddingRight = scrolled ? '12px' : '16px';

				// Apply classes after styles
				if (scrolled) {
					mobileContent.classList.add('navbar--mobile-scrolled');
					mobileContent.classList.remove('navbar--mobile-initial');
				} else {
					mobileContent.classList.add('navbar--mobile-initial');
					mobileContent.classList.remove('navbar--mobile-scrolled');
				}
			} else {
				// Use the same timeline approach for mobile
				if (!currentAnimation) {
					const mobileTimeline = anime.timeline({
						duration: 500,
						easing: 'easeOutCubic',
						complete: () => {
							// Apply final classes after animation completes
							if (scrolled) {
								mobileContent?.classList.add('navbar--mobile-scrolled');
								mobileContent?.classList.remove('navbar--mobile-initial');
							} else {
								mobileContent?.classList.add('navbar--mobile-initial');
								mobileContent?.classList.remove('navbar--mobile-scrolled');
							}
						}
					});

					// Add mobile animations to timeline - keep width at 100% always
					mobileTimeline
						.add({
							targets: mobileWrapper,
							width: '100%',
							translateY: scrolled ? 24 : 0,
						})
						.add({
							targets: mobileContent,
							paddingLeft: scrolled ? '12px' : '16px',
							paddingRight: scrolled ? '12px' : '16px',
						}, 0); // Start at the same time
				}

				// Apply visual classes immediately for CSS transitions
				if (scrolled) {
					mobileContent.classList.add('navbar--mobile-scrolled');
					mobileContent.classList.remove('navbar--mobile-initial');
				} else {
					mobileContent.classList.add('navbar--mobile-initial');
					mobileContent.classList.remove('navbar--mobile-scrolled');
				}
			}
		}

		// Update main navbar classes
		if (navbar) {
			if (scrolled) {
				navbar.classList.add("navbar--scrolled");
				navbar.classList.remove("navbar--initial");
			} else {
				navbar.classList.add("navbar--initial");
				navbar.classList.remove("navbar--scrolled");
			}
		}
	}

	function scrollHandler() {
		const scrollThreshold = 60;
		const shouldBeScrolled = window.scrollY > scrollThreshold;

		// Add small delay to prevent rapid state changes
		if (shouldBeScrolled !== isScrolled) {
			animateNavbar(shouldBeScrolled);
		}
	}

	function initNav() {
		// Check motion preferences
		checkReducedMotion();

		// Listen for changes in motion preferences
		window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', checkReducedMotion);

		navbar = document.getElementById("nav__container");
		desktopWrapper = document.getElementById("nav__desktop-wrapper");
		desktopContent = document.getElementById("nav__desktop-content");
		mobileWrapper = document.getElementById("nav__mobile-wrapper");
		mobileContent = document.getElementById("nav__mobile-content");

		// Initialize with proper classes and initial state
		desktopContent?.classList.add('navbar--desktop-initial');
		mobileContent?.classList.add('navbar--mobile-initial');

		// Ensure desktop navbar starts at 95% width
		if (desktopWrapper) {
			desktopWrapper.style.width = '95%';
			desktopWrapper.style.transform = 'translateY(0px)';
		}
		if (desktopContent) {
			desktopContent.style.paddingLeft = '0px';
			desktopContent.style.paddingRight = '0px';
		}

		// Ensure mobile navbar always stays at 100% width
		if (mobileWrapper) {
			mobileWrapper.style.width = '100%';
		}

		// Add scroll listener with optimized throttling
		let ticking = false;
		let lastScrollY = 0;

		const optimizedScrollHandler = () => {
			const currentScrollY = window.scrollY;

			// Only process if scroll position changed significantly
			if (Math.abs(currentScrollY - lastScrollY) > 5) {
				scrollHandler();
				lastScrollY = currentScrollY;
			}
			ticking = false;
		};

		window.addEventListener("scroll", () => {
			if (!ticking) {
				requestAnimationFrame(optimizedScrollHandler);
				ticking = true;
			}
		}, { passive: true });

		// Initial check in case page is already scrolled
		setTimeout(() => {
			scrollHandler();
		}, 100);
	}

	// runs on initial page load
	initNav();

	// runs on view transitions navigation
	document.addEventListener("astro:after-swap", initNav);
</script>