---
import { Icon } from "astro-icon/components";
import NavLink from "@components/Nav/NavLink.astro";
import MobileNavDropdown from "./MobileNavDropdown.astro";

// data
import navData from "@config/navData.json.ts";
---

<!-- Mobile Menu Overlay - positioned at body level for full viewport coverage -->
<div
	id="mobile-nav__content"
	class="whitepace-nowrap bg-base-50 dark:bg-base-950 fixed top-0 -right-72 z-40 hidden h-screen w-72 items-center overflow-x-hidden text-lg font-normal transition-all duration-300"
>
	<div class="w-full px-2 pb-6">
		<div class="mx-1 my-2 flex w-full justify-end pr-4 pl-6">
			<button id="mobile-nav__close" class="primary-focus p-2" aria-label="close navigation menu">
				<!-- "X" icon to close menu -->
				<Icon
					name="tabler/x"
					class="text-base-600 dark:text-base-400 h-8 w-8"
					aria-hidden="true"
				/>
			</button>
		</div>

		<!-- nav items -->
		<hr class="border-base-200 dark:border-base-800 mx-1" />
		<nav>
			<ul class="mx-1 mt-2 text-xl">
				{
					navData.map((navItem) =>
						"dropdown" in navItem ? (
							// Use the modular dropdown component
							<MobileNavDropdown navItem={navItem} />
						) : (
							// Normal nav link
							<li>
								<NavLink navItem={navItem} class="block px-4 py-2 text-base-900 dark:text-base-100 hover:bg-base-100 dark:hover:bg-base-800 rounded" />
							</li>
						)
					)
				}
			</ul>
		</nav>
	</div>
</div>

<!-- backdrop button to also close menu -->
<button
	id="mobile-nav__backdrop"
	class="mobile-nav__backdrop--fade-out fixed inset-0 z-30 h-screen w-screen bg-black"
	aria-label="close navigation menu"
></button>

<!-- Mobile Menu Styles -->
<style>
	.mobile-nav__backdrop--fade-in {
		animation: MobileNavFadeInAnimation ease-in-out 0.3s forwards;
		display: block;
		width: 100vw;
		height: 100vh;
	}

	.mobile-nav__backdrop--fade-out {
		display: none;
		width: 100vw;
		height: 100vh;
		opacity: 0;
	}

	@keyframes MobileNavFadeInAnimation {
		0% {
			opacity: 0;
		}
		100% {
			opacity: 0.4;
		}
	}

	/* mobile nav content drawer */
	.mobile-nav--slide-in {
		animation: mobileNavSlideInAnimation ease-in-out 0.3s forwards;
	}

	.mobile-nav--slide-out {
		animation: mobileNavSlideOutAnimation ease-in-out 0.3s forwards;
	}

	@keyframes mobileNavSlideInAnimation {
		0% {
			right: -288px; /* -72 * 4px */
		}
		100% {
			right: 0;
		}
	}

	@keyframes mobileNavSlideOutAnimation {
		0% {
			right: 0;
		}
		100% {
			right: -288px; /* -72 * 4px */
		}
	}

	/* Dropdown styles */
	.rotate-180 {
		transform: rotate(180deg);
	}
</style>

<!-- Mobile Menu Script -->
<script>
	let mobileNavBurger: HTMLElement | null;
	let mobileNavContent: HTMLElement | null;
	let mobileNavCloseBtn: HTMLElement | null;
	let mobileNavBackdrop: HTMLElement | null;

	function toggleMobileNav(event: Event) {
		if (mobileNavBurger && mobileNavContent && mobileNavBackdrop) {
			const isOpen = mobileNavContent.classList.contains('mobile-nav--slide-in');

			if (!isOpen) {
				// Open menu
				mobileNavBurger.setAttribute("aria-expanded", "true");

				// Show content
				mobileNavContent.classList.remove("hidden");
				mobileNavContent.classList.remove("mobile-nav--slide-out");
				mobileNavContent.classList.add("mobile-nav--slide-in");

				// Show backdrop
				mobileNavBackdrop.classList.remove("hidden");
				mobileNavBackdrop.classList.remove("mobile-nav__backdrop--fade-out");
				mobileNavBackdrop.classList.add("mobile-nav__backdrop--fade-in");
			} else {
				// Close menu
				mobileNavBurger.setAttribute("aria-expanded", "false");

				// Hide content
				mobileNavContent.classList.remove("mobile-nav--slide-in");
				mobileNavContent.classList.add("mobile-nav--slide-out");
				setTimeout(() => {
					mobileNavContent?.classList.add("hidden");
				}, 300);

				// Hide backdrop
				mobileNavBackdrop.classList.remove("mobile-nav__backdrop--fade-in");
				mobileNavBackdrop.classList.add("mobile-nav__backdrop--fade-out");
				setTimeout(() => {
					mobileNavBackdrop?.classList.add("hidden");
				}, 300);
			}
		}
		event.preventDefault();
		return false;
	}

	function initMobileNav() {
		mobileNavBurger = document.getElementById("mobile-nav__burger");
		mobileNavContent = document.getElementById("mobile-nav__content");
		mobileNavCloseBtn = document.getElementById("mobile-nav__close");
		mobileNavBackdrop = document.getElementById("mobile-nav__backdrop");

		if (mobileNavBurger && mobileNavCloseBtn && mobileNavBackdrop) {
			mobileNavBurger.addEventListener("click", toggleMobileNav);
			mobileNavCloseBtn.addEventListener("click", toggleMobileNav);
			mobileNavBackdrop.addEventListener("click", toggleMobileNav);
		}
	}

	// runs on initial page load
	initMobileNav();

	// runs on view transitions navigation
	document.addEventListener("astro:after-swap", initMobileNav);
</script>
