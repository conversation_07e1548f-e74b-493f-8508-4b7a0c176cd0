---
interface Props {
	href: string;
	newTab?: boolean; // defaults to false
	rest?: Partial<HTMLAnchorElement>; // catch-all for any additional parameters, such as "aria-label"
}

const { href, newTab = false, ...rest } = Astro.props as Props;
---

<a
	href={href}
	class="text-base-500 hover:text-base-600 dark:text-base-400 dark:hover:text-base-300 px-4 py-2 transition underline md:no-underline md:hover:underline"
	target={newTab ? "_blank" : undefined}
	rel={newTab ? "noopener noreferrer" : undefined}
	{...rest}
>
	<slot />
</a>