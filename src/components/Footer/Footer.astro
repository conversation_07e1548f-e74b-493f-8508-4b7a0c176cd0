---
import { Icon } from "astro-icon/components";

// components
import FooterLink from "@components/Footer/FooterLink.astro";
import SiteLogo from "@components/SiteLogo/SiteLogo.astro";
import Button from "@components/Button/Button.astro";
import SocialIcon from "@components/SocialIcon/SocialIcon.astro";
import NewsletterSignup from "@components/Newsletter/NewsletterSignup.astro";

// data
import siteData from "@config/siteData.json.ts";

const today = new Date();
---

<footer class="mt-12 md:mt-16 py-12 md:py-18 lg:py-20 bg-base-100 rounded-t-4xl shadow-xl drop-shadow-lg">
	<div class="site-container">
		<!-- Main Footer Content -->
		<div class="grid grid-cols-1 gap-x-[8vw] gap-y-12 pb-12 md:gap-y-16 md:pb-18 lg:grid-cols-[0.75fr_1fr] lg:gap-y-4 lg:pb-20">
			<!-- Logo and Newsletter Section -->
			<div class="flex flex-col">
				<!-- <PERSON>ui mettere il logo, si trova sopra la newsletter -->
				<NewsletterSignup />
			</div>

			<!-- Contact Info and Social Media Section -->
			<div class="flex flex-col justify-start lg:justify-end items-start lg:items-end">
				<!-- Contact Information -->
				<div class="flex flex-col gap-3 mb-6">
					<!-- Email -->
					<div class="flex items-center gap-3">
						<div class="bg-primary-500 inline-flex size-8 items-center justify-center overflow-hidden rounded-full">
							<Icon name="tabler/mail" class="size-4 text-white" />
						</div>
						<a
							href={`mailto:${siteData.contact.email}`}
							class="text-base-500 hover:text-base-600 dark:text-base-400 dark:hover:text-base-300 transition text-sm underline md:no-underline md:hover:underline"
						>
							{siteData.contact.email}
						</a>
					</div>

					<!-- WhatsApp -->
					<div class="flex items-center gap-3">
						<div class="bg-primary-500 inline-flex size-8 items-center justify-center overflow-hidden rounded-full">
							<Icon name="tabler/brand-whatsapp" class="size-4 text-white" />
						</div>
						<a
							href={`https://wa.me/${siteData.contact.phone.replace(/\D/g, '')}?text=${encodeURIComponent('Ciao! Hai qualche domanda o dubbio? Scrivimi pure, sono qui per aiutarti! 😊')}`}
							class="text-base-500 hover:text-base-600 dark:text-base-400 dark:hover:text-base-300 transition text-sm underline md:no-underline md:hover:underline"
							target="_blank"
							rel="noopener noreferrer"
						>
							{siteData.contact.phone}
						</a>
					</div>

					<!-- Address -->
					<div class="flex items-start gap-3">
						<div class="bg-primary-500 inline-flex size-8 items-center justify-center overflow-hidden rounded-full mt-0.5">
							<Icon name="tabler/map-pin" class="size-4 text-white" />
						</div>
						<button
							id="footer-address-button"
							class="text-base-500 hover:text-base-600 dark:text-base-400 dark:hover:text-base-300 transition bg-transparent border-none p-0 text-left cursor-pointer text-sm underline md:no-underline md:hover:underline"
							data-address1={siteData.contact.address1}
							data-address2={siteData.contact.address2}
						>
							<div class="flex flex-col">
								<span>{siteData.contact.address1}</span> 
								<span>{siteData.contact.address2}</span>
							</div>
						</button>
					</div>
				</div>

				<!-- Social Media Icons -->
				<div class="flex gap-4 mb-0">
					<!-- <SocialIcon
						href="https://twitter.com/BowTiedWebReapr"
						icon="tabler/brand-x"
						social="twitter (x)"
					/>
					<SocialIcon
						href="https://facebook.com"
						icon="tabler/brand-facebook"
						social="facebook"
					/> -->
					<SocialIcon
						href="https://instagram.com"
						icon="tabler/brand-instagram"
						social="instagram"
					/>
					<SocialIcon
						href="https://linkedin.com"
						icon="tabler/brand-linkedin"
						social="linkedin"
					/>
				</div>
			</div>
		</div>

		<!-- Footer Bottom -->
		<div class="h-px w-full bg-base-300 dark:bg-base-800"></div>
		<div class="flex flex-col-reverse items-start justify-between pb-4 pt-6 text-sm md:flex-row md:items-center md:pb-0 md:pt-8">
			<p class="mt-6 md:mt-0 text-base-500">
				&copy; {today.getFullYear()} {siteData.name}. Tutti i diritti riservati.
			</p>
			<ul class="grid grid-flow-row grid-cols-[max-content] justify-center gap-y-4 text-sm md:grid-flow-col md:gap-x-3 md:gap-y-0">
				<li>
					<FooterLink href="/privacy-policy">Privacy Policy</FooterLink>
				</li>
				<li>
					<FooterLink href="/terms">Termini di Servizio</FooterLink>
				</li>
				<li>
					<FooterLink href="/elements">Elementi</FooterLink>
				</li>
			</ul>
		</div>
	</div>
</footer>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		const footerAddressButton = document.getElementById('footer-address-button');

		if (footerAddressButton) {
			footerAddressButton.addEventListener('click', function() {
				const address1 = this.getAttribute('data-address1');
				const address2 = this.getAttribute('data-address2');
				const fullAddress = `${address1}, ${address2}`;
				const encodedAddress = encodeURIComponent(fullAddress);

				// Rileva il dispositivo e il browser
				const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
				const isAndroid = /Android/.test(navigator.userAgent);
				const isMacOS = /Macintosh|MacIntel|MacPPC|Mac68K/.test(navigator.userAgent);

				function tryNativeApp(nativeUrl, fallbackUrl) {
					// Crea un iframe nascosto per provare l'app nativa senza errori visibili
					const iframe = document.createElement('iframe');
					iframe.style.display = 'none';
					iframe.src = nativeUrl;
					document.body.appendChild(iframe);

					// Rimuovi l'iframe dopo un breve periodo
					setTimeout(() => {
						document.body.removeChild(iframe);
					}, 100);

					// Fallback dopo un breve ritardo
					setTimeout(() => {
						window.open(fallbackUrl, '_blank');
					}, 300);
				}

				if (isIOS) {
					// Su iOS, prova Apple Maps
					const appleUrl = `maps://maps.apple.com/?q=${encodedAddress}`;
					const fallbackUrl = `https://maps.google.com/maps?q=${encodedAddress}`;
					tryNativeApp(appleUrl, fallbackUrl);

				} else if (isAndroid) {
					// Su Android, prova Google Maps
					const geoUrl = `geo:0,0?q=${encodedAddress}`;
					const fallbackUrl = `https://maps.google.com/maps?q=${encodedAddress}`;
					tryNativeApp(geoUrl, fallbackUrl);

				} else if (isMacOS) {
					// Su macOS, prova prima Apple Maps, poi Google Maps
					const appleUrl = `maps://?q=${encodedAddress}`;
					const fallbackUrl = `https://maps.google.com/maps?q=${encodedAddress}`;
					tryNativeApp(appleUrl, fallbackUrl);

				} else {
					// Su altri desktop, apri direttamente Google Maps
					window.open(`https://maps.google.com/maps?q=${encodedAddress}`, '_blank');
				}
			});
		}
	});
</script>