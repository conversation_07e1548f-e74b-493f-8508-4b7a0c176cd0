---
// @ts-nocheck

// data
import siteData from "@config/siteData.json.ts";
---

<div class="flex flex-col">
	<p class="mb-5 md:mb-6 text-base-500">
		Iscriviti alla nostra newsletter per rimanere aggiornato su novità e contenuti.
	</p>
	<div class="w-full max-w-md">
		<form
			id="newsletter-form"
			name="newsletter"
			method="POST"
			action="#"
			class="mb-3 grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-[1fr_max-content] md:gap-y-4"
		>

			<!-- Honeypot field to catch bots -->
			<label for="newsletter-emai" class="sr-only">Real users skip this field</label>
			<input id="newsletter-emai" type="text" name="emai" class="sr-only" tabindex="-1" />
			<div class="relative flex w-full items-center">
				<input
					type="email"
					name="email"
					id="newsletter-email"
					class="flex size-full min-h-11 border border-base-300 dark:border-base-700 bg-background rounded-md py-2 px-3 text-sm placeholder:text-base-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50"
					placeholder="Inserisci la tua email"
					required
				/>
			</div>
			<button
				type="submit"
				class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary-500 text-white hover:bg-primary-600 px-5 py-2 min-h-11"
			>
				<!-- Loading indicator -->
				<svg
					id="newsletter-loading"
					xmlns="http://www.w3.org/2000/svg"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					stroke-width="2"
					stroke-linecap="round"
					stroke-linejoin="round"
					class="hidden size-4 animate-spin mr-2"
				>
					<path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
					<path d="M12 3a9 9 0 1 0 9 9"></path>
				</svg>
				<span id="newsletter-button-text">Iscriviti</span>
			</button>
		</form>
		<div>
			<p class="text-xs text-base-400">
				Iscrivendoti accetti la nostra
				<a href="/privacy-policy" class="underline hover:text-base-600">Privacy Policy</a>
				e acconsenti a ricevere aggiornamenti.
			</p>
		</div>

		<!-- Success feedback message -->
		<dialog
			id="newsletter-good-feedback"
			class="invisible fixed bottom-4 left-1/2 z-50 mx-auto flex -translate-x-1/2 rounded-lg bg-green-100 border border-green-300 p-0 text-green-800 opacity-0 transition-all duration-300 shadow-lg"
		>
			<div class="p-4 text-center">
				<p class="font-medium">✅ Iscrizione completata!</p>
				<p class="text-sm mt-1">Grazie per esserti iscritto alla newsletter.</p>
			</div>
		</dialog>

		<!-- Error feedback message -->
		<dialog
			id="newsletter-bad-feedback"
			class="invisible fixed bottom-4 left-1/2 z-50 mx-auto flex -translate-x-1/2 rounded-lg bg-red-100 border border-red-300 p-0 text-red-800 opacity-0 transition-all duration-300 shadow-lg"
		>
			<div class="p-4 text-center">
				<p class="font-medium">❌ Errore nell'iscrizione</p>
				<p class="text-sm mt-1">Riprova o contattaci direttamente.</p>
			</div>
		</dialog>
	</div>
</div>

<script>
	function initNewsletter() {
		const form = document.getElementById('newsletter-form') as HTMLFormElement;
		const emailInput = document.getElementById('newsletter-email') as HTMLInputElement;

		if (form && emailInput) {
			form.addEventListener('submit', (e) => {
				e.preventDefault();
				const email = emailInput.value.trim();
				if (!email) {
					return;
				}

				// Placeholder per futura integrazione MailerLite
				alert('Newsletter temporaneamente non disponibile. Contattaci direttamente per rimanere aggiornato!');
			});
		}
	}

	// Inizializza al caricamento della pagina
	initNewsletter();

	// Reinizializza dopo le transizioni di Astro
	document.addEventListener('astro:after-swap', initNewsletter);
</script>
