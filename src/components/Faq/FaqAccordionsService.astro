---
/**
 * * FAQ section in accordions for service pages, left header text with right faq items
 * using the Accordion component - accepts FAQ data as props
 */
import type { FaqItem } from "../../config/types/configDataTypes";

// components
import Accordion from "@components/Accordion/Accordion.astro";
import Badge from "@components/Badge/Badge.astro";

interface Props {
	faqData: FaqItem[];
	title?: string;
	description?: string;
}

const { 
	faqData, 
	title = "Domande Frequenti",
	description = "Risposte chiare e dirette alle domande più comuni su questo servizio. Se hai altri dubbi, non esitare a contattarmi."
} = Astro.props;
---

<section id="faq-accordions-service" class="overflow-hidden pt-24 pb-16 md:py-28">
	<div class="site-container md:px-10 grid gap-10 md:grid-cols-2">
		<div class="mr-auto max-w-md" data-aos="fade-right">
			<Badge>FAQ</Badge>
			<h2 class="h2 mb-6">{title}</h2>
			<p class="description md:text-md">
				{description}
			</p>
		</div>
		<div class="flex flex-col gap-3" data-aos="fade-left">
			{faqData.map(({ question, answer }) => <Accordion title={question} details={answer} />)}
		</div>
	</div>
</section>
