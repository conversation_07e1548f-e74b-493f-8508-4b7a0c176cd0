---
/**
 * * FAQ section in accordions, left header text with right faq items
 * using the Accordion component
 */
import { Icon } from "astro-icon/components";

// components
import Accordion from "@components/Accordion/Accordion.astro";
import Badge from "@components/Badge/Badge.astro";

// data
import { faqData } from "@config/faqData.json.ts";
---

<section id="faq-accordions" class="overflow-hidden pt-24 pb-16 md:py-28">
	<div class="site-container md:px-10 grid gap-10 md:grid-cols-2">
		<div class="mr-auto max-w-md" data-aos="fade-right">
			<Badge>FAQ</Badge>
			<h2 class="h2 mb-6">Risposte Chiare per un Percorso Consapevole</h2>
			<p class="description md:text-md">
				È normale e, soprattutto, è giusto avere dei dubbi prima di intraprendere un nuovo percorso e affidare la propria salute a un professionista.
				Per questo ho raccolto qui le domande più importanti e comuni che mi vengono poste. 
				Il mio obiettivo è darti risposte oneste e senza giri di parole, per aiutarti a fare una scelta informata. 
				La fiducia, per me, si costruisce sulla trasparenza.
			</p>
		</div>
		<div class="flex flex-col gap-3" data-aos="fade-left">
			{faqData.map(({ question, answer }) => <Accordion title={question} details={answer} />)}
		</div>
	</div>
</section>