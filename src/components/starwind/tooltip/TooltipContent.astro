---
import type { HTMLAttributes } from "astro/types";

type Props = HTMLAttributes<"div"> & {
	/**
	 * Side of the tooltip
	 * @default top
	 */
	side?: "top" | "right" | "bottom" | "left";
	/**
	 * Alignment of the tooltip
	 * @default center
	 */
	align?: "start" | "center" | "end";
	/**
	 * Offset distance in pixels
	 * @default 4
	 */
	sideOffset?: number;
	/**
	 * Prevent the tooltip from colliding with other elements
	 * @default true
	 */
	avoidCollisions?: boolean;
	/**
	 * Open and close animation duration in milliseconds
	 * @default 150
	 */
	animationDuration?: number;
};

const {
	side = "top",
	align = "center",
	sideOffset = 4,
	avoidCollisions = true,
	animationDuration = 150,
	class: className,
} = Astro.props;
---

<div
	class:list={[
		"starwind-tooltip-content",
		"absolute z-50 hidden px-3 py-1.5 whitespace-nowrap shadow-sm",
		"bg-popover text-popover-foreground rounded-md border",
		"animate-in fade-in-0 zoom-in-95",
		"data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",
		{
			"slide-in-from-right-2 right-(--tooltip-offset)": side === "left",
			"slide-in-from-left-2 left-(--tooltip-offset)": side === "right",
			"slide-in-from-top-2 top-(--tooltip-offset)": side === "bottom",
			"slide-in-from-bottom-2 bottom-(--tooltip-offset)": side === "top",
		},
		{
			"left-[50%] translate-x-[-50%]": align === "center" && (side === "top" || side === "bottom"),
			"top-[50%] translate-y-[-50%]": align === "center" && (side === "left" || side === "right"),
			"left-0": align === "start" && (side === "top" || side === "bottom"),
			"right-0": align === "end" && (side === "top" || side === "bottom"),
			"top-0": align === "start" && (side === "left" || side === "right"),
			"bottom-0": align === "end" && (side === "left" || side === "right"),
		},
		className,
	]}
	data-state="closed"
	data-side={side}
	data-align={align}
	{...avoidCollisions && { "data-avoid-collisions": "" }}
	role="tooltip"
	style={{
		"--tooltip-offset": `calc(100% + ${sideOffset}px)`,
		animationDuration: `${animationDuration}ms`,
	}}
>
	<slot> My tooltip! </slot>
</div>