---
import Check from "@tabler/icons/outline/check.svg";

import type { HTMLAttributes } from "astro/types";

type Props = HTMLAttributes<"div"> & {
	/**
	 * The value associated with this select item
	 */
	value: string;
	/**
	 * Whether this select item is disabled and cannot be selected
	 */
	disabled?: boolean;
};

const { class: className, value, disabled, ...rest } = Astro.props;
---

<div
	class:list={[
		"relative flex w-full cursor-default items-center rounded-sm py-1.5 pr-2 pl-8 outline-none select-none",
		"focus:bg-accent focus:text-accent-foreground",
		"data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
		"not-aria-selected:[&_svg]:hidden aria-selected:[&_svg]:flex",
		className,
	]}
	data-value={value}
	data-disabled={disabled}
	aria-selected="false"
	role="option"
	tabindex="0"
	{...rest}
>
	<span class="absolute left-2 size-3.5 items-center justify-center">
		<Check class="size-4" />
	</span>
	<span>
		<slot />
	</span>
</div>