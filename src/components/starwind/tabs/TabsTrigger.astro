---
import type { HTMLAttributes } from "astro/types";

interface Props extends Omit<HTMLAttributes<"button">, "type" | "id" | "role"> {
	value: string;
}

const { value, class: className, ...rest } = Astro.props;
---

<button
	class:list={[
		"inline-flex grow items-center justify-center rounded-sm px-3 py-1.5 font-medium whitespace-nowrap transition-colors",
		"data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
		"focus-visible:outline-outline focus-visible:outline-2 focus-visible:outline-offset-2",
		"disabled:pointer-events-none disabled:opacity-50",
		className,
	]}
	data-tabs-trigger
	data-value={value}
	data-state="inactive"
	role="tab"
	aria-selected="false"
	{...rest}
>
	<slot />
</button>