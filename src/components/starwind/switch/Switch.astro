---
import type { HTMLAttributes } from "astro/types";

/**
 * Props for the Switch component.
 */
type Props = Omit<HTMLAttributes<"button">, "role" | "type" | "aria-checked"> & {
	/**
	 * Unique identifier for the switch component.
	 */
	id: string;
	/**
	 * Optional text label to display alongside the switch.
	 */
	label?: string;
	/**
	 * Controls the checked state of the switch.
	 */
	checked?: boolean;
	/**
	 * Custom padding in pixels to apply around the switch.
	 */
	padding?: number;
	/**
	 * Size variant of the switch component.
	 */
	size?: "sm" | "md" | "lg";
	/**
	 * Visual style variant of the switch.
	 */
	variant?: "primary" | "secondary" | "default";
};

const {
	id,
	label,
	checked = false,
	padding,
	size = "md",
	variant = "default",
	class: className,
	...rest
} = Astro.props;

// if no specific padding is set, base it off of size
let newPadding = padding;
if (!padding) {
	newPadding = size === "sm" ? 2 : size === "lg" ? 4 : 3;
}

const sizeMultiplier = size === "sm" ? 4 : size === "lg" ? 6 : 5;

const toggleSizeClass = {
	sm: "size-4",
	md: "size-5",
	lg: "size-6",
};

let ariaLabel;
if (rest["aria-label"]) {
	ariaLabel = rest["aria-label"];
	delete rest["aria-label"];
} else if (label) {
	ariaLabel = label;
} else {
	ariaLabel = "switch";
}
---

<div class="starwind-switch flex items-center">
	<button
		type="button"
		id={id}
		role="switch"
		aria-checked={checked ? "true" : "false"}
		aria-label={ariaLabel}
		class:list={[
			"inline-flex h-(--height) w-(--width) items-center rounded-full transition-colors",
			"group peer ring-offset-background focus-visible:outline-2 focus-visible:outline-offset-2",
			"aria-[checked=false]:bg-input",
			"not-disabled:cursor-pointer disabled:cursor-not-allowed disabled:opacity-50",
			{
				"aria-checked:bg-primary focus:outline-primary": variant === "primary",
				"aria-checked:bg-secondary focus:outline-secondary": variant === "secondary",
				"aria-checked:bg-foreground focus:outline-outline": variant === "default",
			},
			className,
		]}
		style={{
			"--padding": `${newPadding}px`,
			"--height": `calc((var(--spacing) * ${sizeMultiplier}) + (var(--padding) * 2))`,
			"--width": `calc((var(--spacing) * ${sizeMultiplier} * 2) + (var(--padding) * 3))`,
		}}
		{...rest}
	>
		<span
			class:list={[
				"bg-background inline-block transform rounded-full transition-transform",
				"group-aria-checked:translate-x-(--translation) group-aria-[checked=false]:translate-x-(--padding)",
				toggleSizeClass[size],
			]}
			style={{
				"--translation": `calc((var(--spacing) * ${sizeMultiplier}) + (var(--padding) * 2))`,
			}}></span>
	</button>
	{
		label && (
			<label
				for={id}
				class:list={[
					"text-foreground ml-2 font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
					{
						"text-sm": size === "sm",
						"text-base": size === "md",
						"text-lg": size === "lg",
					},
				]}
			>
				{label}
			</label>
		)
	}
</div>

<script>
	import type { SwitchChangeEvent } from "./SwitchTypes";

	/**
	 * Handles the functionality of a switch component
	 * Manages state changes and keyboard interactions
	 */
	class SwitchHandler {
		private switchButton: HTMLButtonElement;

		constructor(switchButton: HTMLButtonElement) {
			this.switchButton = switchButton;
			this.setupEventListeners();
		}

		/**
		 * Sets up click and keyboard event listeners for the switch
		 */
		private setupEventListeners(): void {
			this.switchButton.addEventListener("click", () => this.handleStateChange());
			this.switchButton.addEventListener("keydown", (event) => this.handleKeyDown(event));
		}

		/**
		 * Handles the state change of the switch
		 */
		private handleStateChange(): void {
			if (this.switchButton.disabled) return;

			const isChecked = this.switchButton.getAttribute("aria-checked") === "true";
			const newState = !isChecked;

			this.switchButton.setAttribute("aria-checked", newState.toString());

			// Dispatch custom event with the new state
			const event = new CustomEvent<SwitchChangeEvent["detail"]>("starwind-switch:change", {
				detail: {
					checked: newState,
					switchId: this.switchButton.id,
				},
				bubbles: true,
				cancelable: true,
			});

			this.switchButton.dispatchEvent(event);
		}

		/**
		 * Handles keyboard events for the switch
		 * @param event - The keyboard event
		 */
		private handleKeyDown(event: KeyboardEvent): void {
			if (this.switchButton.disabled) return;

			if (event.key === " " || event.key === "Enter") {
				event.preventDefault();
				this.handleStateChange();
			}
		}
	}

	// Store instances in a WeakMap to avoid memory leaks
	const switchInstances = new WeakMap<HTMLButtonElement, SwitchHandler>();

	const setupSwitches = () => {
		document
			.querySelectorAll<HTMLButtonElement>('.starwind-switch button[role="switch"]')
			.forEach((switchButton) => {
				if (!switchInstances.has(switchButton)) {
					switchInstances.set(switchButton, new SwitchHandler(switchButton));
				}
			});
	};

	setupSwitches();
	document.addEventListener("astro:after-swap", setupSwitches);
</script>