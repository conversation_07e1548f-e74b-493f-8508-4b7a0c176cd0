---
import type { HTMLAttributes } from "astro/types";

type Props = HTMLAttributes<"div"> & {
	/**
	 * The value of the item
	 */
	value: string;
};

const { value, class: className, ...rest } = Astro.props;
---

<div
	class:list={[
		"starwind-accordion-item",
		"border-x border-b first:rounded-t-lg first:border-t last:rounded-b-lg",
		className,
	]}
	data-value={value}
	data-state="closed"
	{...rest}
>
	<slot />
</div>