---
import ChevronDown from "@tabler/icons/outline/chevron-down.svg";

import type { HTMLAttributes } from "astro/types";

type Props = HTMLAttributes<"button">;

const { class: className, ...rest } = Astro.props;
---

<button
	type="button"
	class:list={[
		"starwind-accordion-trigger",
		"flex w-full items-center justify-between gap-4 rounded-md px-5 py-4",
		"text-left font-medium transition-colors hover:underline",
		"[&[data-state=open]>svg]:rotate-180",
		"focus-visible:outline-outline focus-visible:outline-2 focus-visible:outline-offset-0",
		className,
	]}
	aria-expanded="false"
	{...rest}
>
	<slot />
	<ChevronDown class="size-5 shrink-0 transition-transform duration-200" />
</button>