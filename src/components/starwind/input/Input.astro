---
import type { HTMLAttributes } from "astro/types";

type Props = HTMLAttributes<"input"> & {
	size?: "sm" | "md" | "lg";
};

const { size = "md", class: className, ...rest } = Astro.props;
---

<input
	class:list={[
		"border-input bg-background w-full rounded-md border",
		"focus:outline-outline focus:ring-0 focus:outline-2 focus:outline-offset-2",
		"file:text-foreground file:border-0 file:bg-transparent file:text-sm file:font-medium",
		"disabled:cursor-not-allowed disabled:opacity-50",
		"peer placeholder:text-muted-foreground",
		{
			"h-9 px-2 text-sm": size === "sm",
			"h-11 px-3 text-base": size === "md",
			"h-12 px-4 text-lg": size === "lg",
		},
		className,
	]}
	{...rest}
/>