---
import type { HTMLAttributes } from "astro/types";

type Props = HTMLAttributes<"label"> & {
	size?: "sm" | "md" | "lg";
};

const { size = "md", class: className = "", ...rest } = Astro.props;
---

{/* eslint-disable astro/jsx-a11y/label-has-associated-control */}
<label
	class:list={[
		"leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
		{
			"text-sm": size === "sm",
			"text-base": size === "md",
			"text-lg": size === "lg",
		},
		className,
	]}
	{...rest}
>
	<slot />
</label>