---
import X from "@tabler/icons/outline/x.svg";
import type { HTMLAttributes } from "astro/types";

type Props = HTMLAttributes<"dialog"> & {
	/**
	 * Open and close animation duration in milliseconds
	 */
	animationDuration?: number;
};

const { class: className, animationDuration = 200, ...rest } = Astro.props;
---

<!-- dialog overlay -->
<div
	class:list={[
		"starwind-dialog-backdrop fixed inset-0 top-0 left-0 z-50 hidden h-screen w-screen bg-black/80",
		"data-[state=open]:animate-in data-[state=open]:fade-in-0",
		"data-[state=closed]:animate-out data-[state=closed]:fade-out-0",
	]}
	data-state="closed"
	style={{
		animationDuration: `${animationDuration}ms`,
	}}
>
</div>

<dialog
	class:list={[
		"fixed top-16 left-[50%] z-50 translate-x-[-50%] sm:top-[50%] sm:translate-y-[-50%]",
		"bg-background w-full max-w-lg border p-8 shadow-lg sm:rounded-lg",
		"data-[state=open]:animate-in data-[state=closed]:animate-out",
		"data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95 data-[state=open]:slide-in-from-bottom-2",
		"data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[state=closed]:slide-out-to-bottom-2",
		className,
	]}
	data-state="closed"
	{...rest}
	style={{
		animationDuration: `${animationDuration}ms`,
	}}
>
	<slot />
	<button
		type="button"
		class:list={[
			"starwind-dialog-close text-muted-foreground",
			"absolute top-5.5 right-5.5 rounded-sm opacity-70 transition-opacity hover:opacity-100 disabled:pointer-events-none",
			"focus-visible:outline-outline focus-visible:outline-2 focus-visible:outline-offset-2",
		]}
		data-dialog-close
		aria-label="Close dialog"
	>
		<X class="size-5" />
	</button>
</dialog>