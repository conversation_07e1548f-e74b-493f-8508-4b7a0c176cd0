---
// @ts-nocheck

/**
 * Multi-channel booking interface with strategic card-based selection
 * Replaces ContactForm.astro with guided booking experience
 */

import { Icon } from "astro-icon/components";
import Button from "@components/Button/Button.astro";
---

<section id="booking" class="mx-auto max-w-4xl">
	<!-- Initial State: Three Booking Option Cards -->
	<div id="booking-cards" class="flex flex-col gap-6">
		
		<!-- Primary Card: Calendly (Recommended) -->
		<div class="booking-card primary-card border-2 border-primary-500 bg-primary-50 dark:bg-primary-950/20 rounded-lg p-6 relative overflow-hidden">
			<!-- Recommended Badge -->
			<div class="absolute top-4 right-4">
				<!-- <span class="bg-primary-500 text-white text-xs font-semibold px-3 py-1 rounded-full">
					Consigliato
				</span> -->
			</div>
			
			<div class="flex items-start gap-4">
				<div class="bg-primary-500 inline-flex size-12 items-center justify-center rounded-full flex-shrink-0">
					<Icon name="tabler/calendar-check" class="size-6 text-white" />
				</div>
				
				<div class="flex-1">
					<h3 class="text-xl font-semibold text-primary-700 dark:text-primary-300 mb-2">
						Prenota Direttamente Qui
					</h3>
					<p class="text-base-600 dark:text-base-400 mb-4">
						Usa il nostro calendario interattivo per un'esperienza veloce e integrata. 
						Seleziona sede, tipo di visita e trova subito l'orario perfetto per te.
					</p>
					<Button
						variant="primary"
						class="w-full sm:w-auto"
						href="/prenotazione-integrata"
					>
						Inizia la prenotazione
					</Button>
				</div>
			</div>
		</div>

		<!-- Secondary Card: MioDottore -->
		<div class="booking-card secondary-card border border-base-200 dark:border-base-700 bg-white dark:bg-base-900 rounded-lg p-6">
			<div class="flex items-start gap-4">
				<div class="bg-base-100 dark:bg-base-800 inline-flex size-12 items-center justify-center rounded-full flex-shrink-0 border border-base-200 dark:border-base-700">
					<Icon name="tabler/stethoscope" class="size-6 text-base-600 dark:text-base-400" />
				</div>
				
				<div class="flex-1">
					<h3 class="text-lg font-semibold text-base-800 dark:text-base-200 mb-2">
						Prenota su MioDottore
					</h3>
					<p class="text-base-600 dark:text-base-400 mb-4">
						Se sei già registrato sulla loro piattaforma e preferisci usare il loro sistema.
					</p>
					<Button 
						variant="outline" 
						class="w-full sm:w-auto inline-flex items-center gap-2"
						href="https://www.miodottore.it/emanuele-belloni/nutrizionista/bastia-umbra"
						target="_blank"
						rel="noopener noreferrer"
					>
						Vai a MioDottore
						<Icon name="tabler/external-link" class="size-4" />
					</Button>
				</div>
			</div>
		</div>

		<!-- Secondary Card: Doctolib -->
		<div class="booking-card secondary-card border border-base-200 dark:border-base-700 bg-white dark:bg-base-900 rounded-lg p-6">
			<div class="flex items-start gap-4">
				<div class="bg-base-100 dark:bg-base-800 inline-flex size-12 items-center justify-center rounded-full flex-shrink-0 border border-base-200 dark:border-base-700">
					<Icon name="tabler/stethoscope" class="size-6 text-base-600 dark:text-base-400" />
				</div>
				
				<div class="flex-1">
					<h3 class="text-lg font-semibold text-base-800 dark:text-base-200 mb-2">
						Prenota su Doctolib
					</h3>
					<p class="text-base-600 dark:text-base-400 mb-4">
						Alternativa europea per la prenotazione online se preferisci questa piattaforma.
					</p>
					<Button 
						variant="outline" 
						class="w-full sm:w-auto inline-flex items-center gap-2"
						href="https://www.doctolib.it/nutrizionista/bastia-umbra/emanuele-belloni"
						target="_blank"
						rel="noopener noreferrer"
					>
						Vai a Doctolib
						<Icon name="tabler/external-link" class="size-6" />
					</Button>
				</div>
			</div>
		</div>
	</div>


</section>

<style>
	/* Card hover effects */
	.booking-card {
		transition: all 0.3s ease;
		cursor: pointer;
	}

	.primary-card:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 25px rgba(0, 106, 113, 0.15);
	}

	.secondary-card:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
	}

</style>



