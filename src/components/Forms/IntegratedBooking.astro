---
// @ts-nocheck

/**
 * Integrated booking component with Calendly integration
 * Consolidates booking functionality from ContactForm.astro and prenotazione-integrata.astro
 * Follows the modular pattern of MultichannelBooking.astro
 */

import { Icon } from "astro-icon/components";
---

<!-- Integrated Booking Interface -->
<section id="integrated-booking" class="mx-auto max-w-7xl">
	<!-- Back Button -->
	<div class="mb-6 flex items-center gap-3">
		<a
			href="/contatti-e-prenotazioni"
			class="inline-flex items-center gap-2 text-base-600 hover:text-base-800 dark:text-base-400 dark:hover:text-base-200 transition-colors"
		>
			<Icon name="tabler/arrow-left" class="size-4" />
			Torna alle opzioni
		</a>
	</div>

	<!-- Layout: Desktop = Form left, Calendly right | Mobile = Form top, Calendly bottom -->
	<div class="booking-grid grid grid-cols-1 lg:grid-cols-2 gap-8">
		<!-- Calendly Inline Container (Right on desktop, Bottom on mobile) -->
		<div class="order-2 lg:order-2" id="calendly-wrapper">
			<div id="calendly-container" class="min-h-[500px] rounded-lg border border-base-200 dark:border-base-700">
				<!-- ⚡ PERFORMANCE: Calendly loaded ONLY when needed -->
				<!-- Placeholder ottimizzato per performance -->
				<div id="calendly-placeholder" class="flex items-center justify-center h-full text-base-500 dark:text-base-400 px-4">
					<div class="text-center">
						<Icon name="tabler/calendar-check" class="mx-auto mb-2 size-6 sm:size-8" />
						<p class="text-sm sm:text-base">Seleziona la sede e tipo di appuntamento per visualizzare il calendario</p>
						<div class="mt-2 flex items-center justify-center gap-1 text-xs text-green-600 dark:text-green-400">
							
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Contact Form (Left on desktop, Top on mobile) -->
		<div class="order-1 lg:order-1">
			<div class="flex flex-col gap-4">

				<!-- Studio -->
				<div>
					<label for="contact-studio" class="form__label mb-2 block text-sm font-medium text-base-700 dark:text-base-300"
						>Sede dell'appuntamento</label
					>
					<select
						name="studio"
						id="contact-studio"
						class="form__input !h-auto !min-h-[2.75rem] !py-2 !leading-tight w-full rounded-lg border border-base-300 dark:border-base-600 bg-white dark:bg-base-800 px-3 text-base-900 dark:text-base-100 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
						required
						data-select="studio"
					>
						<option value="" disabled hidden>Seleziona..</option> 
						<!-- <option value="todi">Via XXV Aprile 1/L, Todi (PG)</option> -->
						<option value="bastia">KiClub, Via Sacco e Vanzetti 1, Bastia Umbra (PG)</option>
						<!-- <option value="online">Online</option> -->
					</select>
				</div>

				<!-- Tipo di appuntamento-->
				<div>
					<label for="contact-visit-type" class="form__label mb-2 block text-sm font-medium text-base-700 dark:text-base-300"
						>Tipo di appuntamento</label
					>
					<select
						name="visit-type"
						id="contact-visit-type"
						class="form__input !h-auto !min-h-[2.75rem] !py-2 !leading-tight w-full rounded-lg border border-base-300 dark:border-base-600 bg-white dark:bg-base-800 px-3 text-base-900 dark:text-base-100 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
						required
						data-select="visit-type"
					>
						<option value="" disabled hidden>Seleziona..</option>
						<option value="prima-visita">Prima Visita Nutrizionale, 90€</option>
						<option value="controllo">Visita di Controllo, 35€</option>
						<!-- <option value="online">Consulenza Online</option> -->
					</select>
				</div>

			</div>
		</div>
	</div>
</section>

<!-- ⚡ PERFORMANCE: Optimized Critical Styles Only -->
<style>
	/* Essential form styles - critical for functionality */
	.form__input {
		white-space: normal;
		word-wrap: break-word;
		overflow-wrap: break-word;
		width: 100%;
		border-radius: 0.5rem;
		border: 1px solid rgb(209 213 219);
		background-color: white;
		padding: 0.5rem 0.75rem;
		color: rgb(17 24 39);
		min-height: 2.75rem;
		line-height: 1.25;
	}

	.form__input:focus {
		border-color: rgb(59 130 246);
		outline: none;
		box-shadow: 0 0 0 1px rgb(59 130 246);
	}

	.form__input option {
		padding: 0.5rem;
		line-height: 1.4;
	}

	.form__label {
		display: block;
		font-size: 0.875rem;
		font-weight: 500;
		color: rgb(55 65 81);
		margin-bottom: 0.5rem;
	}

	/* Critical Calendly container - prevent CLS */
	#calendly-container {
		border-radius: 0.5rem;
		overflow: hidden;
		transition: min-height 0.3s ease;
		border: 1px solid rgb(229 231 235);
	}

	/* Essential Calendly widget styles */
	.calendly-inline-widget {
		border-radius: 0.5rem;
		width: 100%;
		transition: height 0.3s ease;
	}

	/* Mobile optimizations - critical only */
	@media (max-width: 1023px) {
		.form__input {
			font-size: 0.9rem;
			line-height: 1.3;
		}

		.booking-grid {
			gap: 1.5rem;
		}
	}
</style>


<!-- 🚀 ULTRA-OPTIMIZED JavaScript - ZERO initial load -->
<script>
    // ⚡ PERFORMANCE: Calendly URLs - NO immediate loading
    const calendlyUrls = {
        'bastia-prima-visita': 'https://calendly.com/emanuelebelloni0/prima-visita-kiclub?text_color=212121&primary_color=006a71&hide_gdpr_banner=1',
        'bastia-controllo': 'https://calendly.com/emanuelebelloni0/visita-di-controllo-kiclub?text_color=212121&primary_color=006a71&hide_gdpr_banner=1',
    };

    // ⚡ PERFORMANCE: Variables - lightweight references only
    let studioSelectRef: HTMLSelectElement | null = null;
    let visitTypeSelectRef: HTMLSelectElement | null = null;
    let calendlyScriptLoaded = false;
    let calendlyScriptLoading = false;

    // ⚡ PERFORMANCE: Calendly message handler - defined but not attached
    const handleCalendlyMessage = (e) => {
        if (e.origin !== "https://calendly.com") return;
        if (e.data.event && e.data.event === 'calendly.event_scheduled') {
            window.location.href = '/grazie';
        }
    };

    // ⚡ PERFORMANCE: Setup listener ONLY when Calendly loads
    function setupCalendlyEventListener() {
        window.removeEventListener('message', handleCalendlyMessage);
        window.addEventListener('message', handleCalendlyMessage);
    }

    // --- Gestione Auto-Espansione Calendly ---
    function setupCalendlyAutoExpansion(calendlyDiv) {
        const calendlyContainer = document.getElementById('calendly-container');
        if (!calendlyContainer || !calendlyDiv) return;

        // Observer per monitorare i cambiamenti nel DOM del widget Calendly
        const resizeObserver = new ResizeObserver((entries) => {
            // Verifica che ci siano entries da processare
            if (entries.length > 0) {
                const iframe = calendlyDiv.querySelector('iframe');
                if (iframe) {
                    // Ottieni l'altezza del contenuto dell'iframe
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
                        if (iframeDoc) {
                            const body = iframeDoc.body;
                            const html = iframeDoc.documentElement;
                            const height = Math.max(
                                body?.scrollHeight || 0,
                                body?.offsetHeight || 0,
                                html?.clientHeight || 0,
                                html?.scrollHeight || 0,
                                html?.offsetHeight || 0
                            );

                            if (height > 0) {
                                const isMobile = window.innerWidth < 1024;
                                const minHeight = isMobile ? 600 : 700;
                                const finalHeight = Math.max(height, minHeight);

                                // Applica l'altezza al widget e al container
                                calendlyDiv.style.height = `${finalHeight}px`;
                                calendlyContainer.style.minHeight = `${finalHeight}px`;
                            }
                        }
                    } catch (e) {
                        // Cross-origin restrictions - usa approccio alternativo
                        handleCrossOriginExpansion(iframe, calendlyDiv, calendlyContainer);
                    }
                }
            }
        });

        // Monitora il widget Calendly per cambiamenti
        resizeObserver.observe(calendlyDiv);

        // Cleanup function per rimuovere l'observer
        (calendlyDiv as HTMLElement & { _resizeObserver?: ResizeObserver })._resizeObserver = resizeObserver;

        // Approccio alternativo: monitora i messaggi postMessage da Calendly
        const handleCalendlyResize = (event) => {
            if (event.origin !== 'https://calendly.com') return;

            if (event.data && typeof event.data === 'object') {
                // Calendly invia messaggi con informazioni sull'altezza
                if (event.data.type === 'calendly_height' || event.data.height) {
                    const height = event.data.height || event.data.value;
                    if (height && height > 0) {
                        const isMobile = window.innerWidth < 1024;
                        const minHeight = isMobile ? 600 : 700;
                        const finalHeight = Math.max(height, minHeight);

                        calendlyDiv.style.height = `${finalHeight}px`;
                        calendlyContainer.style.minHeight = `${finalHeight}px`;
                    }
                }
            }
        };

        window.addEventListener('message', handleCalendlyResize);
        (calendlyDiv as HTMLElement & { _messageListener?: EventListener })._messageListener = handleCalendlyResize;
    }

    function handleCrossOriginExpansion(iframe, calendlyDiv, calendlyContainer) {
        // Approccio fallback per cross-origin restrictions
        const isMobile = window.innerWidth < 1024;

        // Usa un'altezza più generosa per evitare scrolling
        const expandedHeight = isMobile ? 900 : 1200;

        calendlyDiv.style.height = `${expandedHeight}px`;
        calendlyContainer.style.minHeight = `${expandedHeight}px`;

        // Monitora il caricamento dell'iframe
        iframe.onload = () => {
            // Dopo il caricamento, prova a ottimizzare l'altezza
            setTimeout(() => {
                // Se possibile, regola l'altezza basandosi sul contenuto visibile
                const rect = iframe.getBoundingClientRect();
                if (rect.height > 0) {
                    const optimizedHeight = Math.max(rect.height + 50, isMobile ? 600 : 700);
                    calendlyDiv.style.height = `${optimizedHeight}px`;
                    calendlyContainer.style.minHeight = `${optimizedHeight}px`;
                }
            }, 2000);
        };
    }

    // ⚡ PERFORMANCE: Dynamic Calendly Script Loader - LAZY LOADING
    async function loadCalendlyScript(): Promise<void> {
        if (calendlyScriptLoaded) return Promise.resolve();
        if (calendlyScriptLoading) {
            // Wait for existing load to complete
            return new Promise((resolve) => {
                const checkLoaded = () => {
                    if (calendlyScriptLoaded) resolve();
                    else setTimeout(checkLoaded, 100);
                };
                checkLoaded();
            });
        }

        calendlyScriptLoading = true;

        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = 'https://assets.calendly.com/assets/external/widget.js';
            script.async = true;

            script.onload = () => {
                calendlyScriptLoaded = true;
                calendlyScriptLoading = false;
                setupCalendlyEventListener();
                resolve();
            };

            script.onerror = () => {
                calendlyScriptLoading = false;
                reject(new Error('Failed to load Calendly script'));
            };

            document.head.appendChild(script);
        });
    }



    // ⚡ PERFORMANCE: Main Logic - ONLY loads when both selections made
    async function checkAndLoadCalendly() {
        const selectedStudio = studioSelectRef?.value;
        const selectedVisitType = visitTypeSelectRef?.value;

        if (selectedStudio && selectedVisitType) {
            const calendlyKey = `${selectedStudio}-${selectedVisitType}`;
            await loadCalendly(calendlyKey);
        } else {
            clearCalendly();
        }
    }

    // ⚡ PERFORMANCE: Optimized Calendly Loader - Production Ready
    async function loadCalendly(key: string) {
        const url = calendlyUrls[key];
        const calendlyContainer = document.getElementById('calendly-container');
        const calendlyWrapper = document.getElementById('calendly-wrapper');

        if (!url || !calendlyContainer) return;

        // Show enhanced loading state with progress info
        calendlyContainer.innerHTML = `
            <div class="flex flex-col items-center justify-center h-full text-base-600 dark:text-base-400">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mb-4"></div>
                <p class="text-sm">Caricamento calendario...</p>
                <p class="text-xs mt-1 opacity-70">Connessione a Calendly in corso</p>
            </div>
        `;

        try {
            // Load Calendly script only when needed
            await loadCalendlyScript();

            const isMobile = window.innerWidth < 1024;
            if (isMobile && calendlyWrapper) {
                calendlyWrapper.style.display = 'block';
            }

            // Clear loading state and create widget
            calendlyContainer.innerHTML = `
                <div class="calendly-inline-widget"
                     data-url="${url}"
                     style="min-height:${isMobile ? '600px' : '700px'};width:100%;">
                </div>
            `;

            const calendlyDiv = calendlyContainer.querySelector('.calendly-inline-widget') as HTMLElement;

            // Initialize Calendly widget
            if ((window as any).Calendly && (window as any).Calendly.initInlineWidget) {
                try {
                    (window as any).Calendly.initInlineWidget({
                        url: url,
                        parentElement: calendlyDiv
                    });
                } catch (error) {
                    // Fallback: trigger auto-initialization by recreating script
                    setTimeout(() => {
                        const existingScript = document.querySelector('script[src*="calendly.com/assets/external/widget.js"]');
                        if (existingScript && calendlyDiv && !calendlyDiv.querySelector('iframe')) {
                            existingScript.remove();
                            const newScript = document.createElement('script');
                            newScript.src = 'https://assets.calendly.com/assets/external/widget.js';
                            newScript.async = true;
                            document.head.appendChild(newScript);
                        }
                    }, 1000);
                }
            }

            // Setup auto-expansion monitoring
            setupCalendlyAutoExpansion(calendlyDiv);

        } catch (error) {
            console.error('💥 Critical error loading Calendly:', error);
            calendlyContainer.innerHTML = `
                <div class="flex flex-col items-center justify-center h-full text-red-500 dark:text-red-400 p-4">
                    <p class="text-sm font-medium mb-2">Errore nel caricamento del calendario</p>
                    <p class="text-xs opacity-70 text-center">Riprova tra qualche istante o contattaci direttamente</p>
                    <button onclick="location.reload()" class="mt-3 px-4 py-2 bg-primary-500 text-white rounded text-xs hover:bg-primary-600">
                        Ricarica pagina
                    </button>
                </div>
            `;
        }
    }

    // ⚡ PERFORMANCE: Clear Calendly - lightweight cleanup
    function clearCalendly() {
        const calendlyContainer = document.getElementById('calendly-container');
        const calendlyPlaceholder = document.getElementById('calendly-placeholder');
        const calendlyWrapper = document.getElementById('calendly-wrapper');

        if (calendlyContainer) {
            // Clean observers and listeners before removing content
            const calendlyWidget = calendlyContainer.querySelector('.calendly-inline-widget') as HTMLElement & {
                _resizeObserver?: ResizeObserver;
                _messageListener?: EventListener;
            };
            if (calendlyWidget) {
                if (calendlyWidget._resizeObserver) {
                    calendlyWidget._resizeObserver.disconnect();
                    delete calendlyWidget._resizeObserver;
                }
                if (calendlyWidget._messageListener) {
                    window.removeEventListener('message', calendlyWidget._messageListener);
                    delete calendlyWidget._messageListener;
                }
            }

            const isMobile = window.innerWidth < 1024;
            calendlyContainer.innerHTML = '';
            calendlyContainer.style.minHeight = '500px';

            if (isMobile) {
                if (calendlyWrapper) calendlyWrapper.style.display = 'none';
            } else {
                if (calendlyPlaceholder) calendlyContainer.appendChild(calendlyPlaceholder);
                if (calendlyWrapper) calendlyWrapper.style.display = 'block';
            }
        }
    }

    // ⚡ PERFORMANCE: Astro Lifecycle - minimal initial setup
    function initPage() {
        // Reset script loading state on page load
        calendlyScriptLoaded = false;
        calendlyScriptLoading = false;

        studioSelectRef = document.getElementById('contact-studio') as HTMLSelectElement;
        visitTypeSelectRef = document.getElementById('contact-visit-type') as HTMLSelectElement;

        if (studioSelectRef && visitTypeSelectRef) {
            studioSelectRef.addEventListener('change', checkAndLoadCalendly);
            visitTypeSelectRef.addEventListener('change', checkAndLoadCalendly);
            // ⚡ PERFORMANCE: NO initial check - wait for user interaction
        }
    }

    // ⚡ PERFORMANCE: Cleanup - thorough but efficient
    function cleanupPage() {
        if (studioSelectRef) studioSelectRef.removeEventListener('change', checkAndLoadCalendly);
        if (visitTypeSelectRef) visitTypeSelectRef.removeEventListener('change', checkAndLoadCalendly);

        window.removeEventListener('message', handleCalendlyMessage);

        // Remove Calendly script and global object
        const calendlyScript = document.querySelector('script[src*="calendly.com"]');
        if (calendlyScript) calendlyScript.remove();
        if ((window as any).Calendly) delete (window as any).Calendly;

        // Reset loading states
        calendlyScriptLoaded = false;
        calendlyScriptLoading = false;

        clearCalendly();
    }

    // ⚡ PERFORMANCE: Astro View Transitions - optimized lifecycle
    document.addEventListener('astro:page-load', initPage);
    document.addEventListener('astro:before-swap', cleanupPage);
</script>
