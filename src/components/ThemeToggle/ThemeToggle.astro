---
import { Icon } from "astro-icon/components";

interface Props {
	class?: string; // any additional classes
	rest?: any; // catch-all for any additional parameters, such as "aria-label"
	animationDuration?: number; // defaults to 300ms
}

const { class: className, animationDuration = 300, ...rest } = Astro.props as Props;
---

<button
	class:list={[className, "theme-toggle primary-focus nav__link--base rounded-full p-2"]}
	aria-label="color theme toggle"
	{...rest}
>
	<!-- placeholder -->
	<div class="relative size-5">
		<Icon
			name="tabler/moon-stars"
			class:list={["theme-toggle__dark-icon absolute inset-0 size-5"]}
			data-current-theme="false"
			style={{
				"--animation-duration": `${animationDuration}ms`,
			}}
		/>
		<Icon
			name="tabler/sun"
			class:list={["theme-toggle__light-icon absolute inset-0 size-5"]}
			data-current-theme="false"
			style={{
				"--animation-duration": `${animationDuration}ms`,
			}}
		/>
	</div>
</button>

<style>
	@keyframes icon-enter {
		0% {
			opacity: 0;
			transform: rotate(180deg);
		}
		100% {
			opacity: 1;
			transform: rotate(0);
		}
	}

	@keyframes icon-exit {
		0% {
			opacity: 1;
			transform: rotate(0);
		}
		100% {
			opacity: 0;
			transform: rotate(-180deg);
		}
	}

	.theme-toggle__dark-icon {
		opacity: 0;
		transform: rotate(180deg);
	}

	.theme-toggle__dark-icon[data-current-theme="true"] {
		opacity: 1;
		transform: rotate(0);
	}

	/* Only apply animations when the can-animate class is present */
	.can-animate .theme-toggle__dark-icon[data-current-theme="true"] {
		animation: icon-enter var(--animation-duration) forwards;
	}

	.can-animate .theme-toggle__dark-icon[data-current-theme="false"] {
		animation: icon-exit var(--animation-duration) forwards;
	}

	.theme-toggle__light-icon {
		opacity: 0;
		transform: rotate(180deg);
	}

	.theme-toggle__light-icon[data-current-theme="true"] {
		opacity: 1;
		transform: rotate(0);
	}

	/* Only apply animations when the can-animate class is present */
	.can-animate .theme-toggle__light-icon[data-current-theme="true"] {
		animation: icon-enter var(--animation-duration) forwards;
	}

	.can-animate .theme-toggle__light-icon[data-current-theme="false"] {
		animation: icon-exit var(--animation-duration) forwards;
	}
</style>

<script>
	/* DARK MODE TEMPORARILY DISABLED - To re-enable, restore the original functions */

	function changeTheme(theme: "dark" | "light") {
		// TEMPORARY: Always force light theme
		document.documentElement.classList.remove("dark");
		localStorage.setItem("colorTheme", "light");

		/* ORIGINAL CODE - COMMENTED OUT FOR TEMPORARY DISABLE
		if (theme === "dark") {
			document.documentElement.classList.add("dark");
			localStorage.setItem("colorTheme", "dark");
		} else {
			document.documentElement.classList.remove("dark");
			localStorage.setItem("colorTheme", "light");
		}
		*/
	}

	function initThemeToggle() {
		const themeToggleDarkIcons = document.querySelectorAll(
			".theme-toggle__dark-icon",
		) as NodeListOf<HTMLElement>;
		const themeToggleLightIcons = document.querySelectorAll(
			".theme-toggle__light-icon",
		) as NodeListOf<HTMLElement>;
		const themeToggleBtns = document.querySelectorAll(".theme-toggle");

		// TEMPORARY: Always show light theme icons
		themeToggleDarkIcons.forEach((themeToggleDarkIcon) => {
			themeToggleDarkIcon.setAttribute("data-current-theme", "false");
		});
		themeToggleLightIcons.forEach((themeToggleLightIcon) => {
			themeToggleLightIcon.setAttribute("data-current-theme", "true");
		});

		// TEMPORARY: Disable theme toggle functionality
		themeToggleBtns.forEach((themeToggleBtn) => {
			// Remove any existing event listeners and disable the button
			themeToggleBtn.setAttribute("disabled", "true");
			themeToggleBtn.setAttribute("title", "Dark mode temporaneamente disabilitata");
			(themeToggleBtn as HTMLElement).style.opacity = "0.5";
			(themeToggleBtn as HTMLElement).style.cursor = "not-allowed";
		});

		/* ORIGINAL CODE - COMMENTED OUT FOR TEMPORARY DISABLE
		const colorTheme = localStorage.getItem("colorTheme");
		// Set initial state without animations
		if (colorTheme === "dark") {
			themeToggleDarkIcons.forEach((themeToggleDarkIcon) => {
				themeToggleDarkIcon.setAttribute("data-current-theme", "true");
			});
			themeToggleLightIcons.forEach((themeToggleLightIcon) => {
				themeToggleLightIcon.setAttribute("data-current-theme", "false");
			});
		} else {
			themeToggleDarkIcons.forEach((themeToggleDarkIcon) => {
				themeToggleDarkIcon.setAttribute("data-current-theme", "false");
			});
			themeToggleLightIcons.forEach((themeToggleLightIcon) => {
				themeToggleLightIcon.setAttribute("data-current-theme", "true");
			});
		}

		// add event listeners to all toggle buttons
		themeToggleBtns.forEach((themeToggleBtn) => {
			themeToggleBtn.addEventListener("click", function () {
				// Enable animations before toggling
				themeToggleBtn.classList.add("can-animate");

				// toggle icons inside button and update data attributes
				themeToggleDarkIcons.forEach((themeToggleDarkIcon) => {
					const isCurrentTheme = themeToggleDarkIcon.getAttribute("data-current-theme") === "true";
					themeToggleDarkIcon.setAttribute("data-current-theme", (!isCurrentTheme).toString());
				});
				themeToggleLightIcons.forEach((themeToggleLightIcon) => {
					const isCurrentTheme = themeToggleLightIcon.getAttribute("data-current-theme") === "true";
					themeToggleLightIcon.setAttribute("data-current-theme", (!isCurrentTheme).toString());
				});

				// update local storage
				if (localStorage.getItem("colorTheme") === "light") {
					changeTheme("dark");
				} else {
					changeTheme("light");
				}
			});
		});
		*/
	}

	// runs on initial page load
	initThemeToggle();

	// runs on view transitions navigation
	document.addEventListener("astro:after-swap", initThemeToggle);
</script>