---
/**
 * * These are cards with icons, titles, text, and a url
 * Recommend using minimum 3 cards in a row, although this should work for any number of cards
 * These smaller cards work great if there are a large amount of services to advertise
 *
 * ! I copy all icons into the src/icons folder and recommend you do the same
 * ! If the icon is at src/icons/tabler/paint.svg then you put in "tabler/paint"
 */

import { Icon } from "astro-icon/components";

// components
import <PERSON><PERSON> from "@components/Button/Button.astro";



interface Props {
	icon: string; // icon string for astro-icon
	title: string;
	text: string;
	href: string;
	idx: number; // determines which "blob" icon svg to use. There are 4 options (0-3)
	rest?: any; // catch-all for any additional parameters, such as "aria-label"
}

const { icon, title, text, href, idx, ...rest } = Astro.props as Props;


---

<div {...rest} class="card-base">
	<a
		href={href}
		class="group dark:hover:bg-base-900 flex h-full flex-col justify-between overflow-hidden rounded-xl px-3 py-8 text-center card-hover-interactive hover:bg-white"
	>
		<div>
			<div
				class="bg-primary-500 mx-auto mb-7 inline-flex size-15 items-center justify-center rounded-lg text-white"
			>
				<Icon name={icon} class="size-7 text-white" aria-hidden="true" />
			</div>

			<h3 class="text-base-900 dark:text-base-100 text-xl leading-tight font-bold tracking-tight md:text-xl mb-3">{title}</h3>
			<p class="description text-pretty">{text}</p>
		</div>
		<div class="mt-6 flex justify-center">
			<Button variant="outline" arrow="right">
				Scopri di più <span class="sr-only">su {title}</span>
			</Button>
		</div>
	</a>
</div>