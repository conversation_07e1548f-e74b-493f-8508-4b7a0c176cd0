---
/**
 * * These are small cards with icons, titles, and text
 * This looks best with an even number of cards. They are smaller to
 * use with more cards in the same section. 6 cards is probably ideal
 *
 * ! I copy all icons into the src/icons folder and recommend you do the same
 * ! If the icon is at src/icons/tabler/paint.svg then you put in "tabler/paint"
 */

import { Icon } from "astro-icon/components";

interface Props {
	icon: string;
	title: string;
	text: string;
	class?: string; // for grid layout classes
	rest?: any; // catch-all for any additional parameters, such as "aria-label"
}

const { icon, title, text, class: className, ...rest } = Astro.props as Props;
---

<div {...rest} class={`card-base ${className || ''}`}>
	<div
		class="dark:hover:bg-base-900 h-full rounded-xl p-4 card-hover-subtle hover:bg-white"
	>
		<div class="flex items-start gap-2">
			<Icon name={icon} class="text-primary-500 mt-1 h-6 w-6" aria-hidden="true" />
			<h3 class="text-base-900 dark:text-base-100 inline text-xl font-bold">
				{title}
			</h3>
		</div>

		<p class="description mt-2">{text}</p>
	</div>
</div>