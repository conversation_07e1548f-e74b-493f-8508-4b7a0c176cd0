---
/**
 * * These are larger cards with icons, titles, and text
 * Recommend using minimum 3 cards in a row, although this should work for any number of cards
 * These smaller cards work great if there are a large amount of services to advertise
 *
 * ! If you can an error for an icon like "Error: Not Found: "calendar-check" in pack "tabler",
 * ! You can copy to actual SVG into src/icons, name it whatever, and use similar to below example "tabler/calendar-check"
 */

// components
import ServiceCard from "@components/ServiceCard/ServiceCardIcon.astro";
import Badge from "@components/Badge/Badge.astro";

// data
import { servicesData } from "@/data/servicesData";
---

<section id="services-icon" class="py-24 md:py-28">
	<div class="mx-auto max-w-[85rem] px-8 md:px-12 lg:px-16">
		<!-- Badge-heading-description container with pattern background -->
		<div
			class="mx-auto mb-16 text-center md:max-w-4xl relative overflow-hidden bg-[url('/assets/pattern-light.svg')] bg-top bg-no-repeat dark:bg-[url('/assets/pattern-dark-big.svg')] py-8 rounded-2xl"
			data-aos="fade-up"
		>
			<!-- Content with proper z-index to appear above pattern -->
			<div class="relative z-10">
				<Badge>📌 Servizi</Badge>
				<h2 class="h2 mb-4">Servizi di Precisione per la Salute e la Performance</h2>
				<p class="description text-md md:text-md">
					Ogni percorso è un progetto ingegnerizzato che fonde nativamente Nutrizione e Chinesiologia, basato su dati oggettivi e finalizzato a un unico scopo:
					il raggiungimento misurabile dei tuoi obiettivi. Che la tua meta sia clinica, estetica o agonistica, qui troverai la strategia scientifica per raggiungerla.
				</p>
			</div>
		</div>

		<!-- Services cards grid without background pattern -->
		<div class="grid gap-4 sm:grid-cols-2 md:grid-cols-3" id="services-icon-cards">
			{
				servicesData.map((feature, idx) => (
					<ServiceCard
						title={feature.title}
						text={feature.text}
						icon={feature.icon}
						href={feature.href}
						idx={idx}
						data-aos="zoom-in"
						data-aos-delay={0.15 * idx}
						data-aos-trigger="#services-icon-cards"
					/>
				))
			}
		</div>
	</div>
</section>