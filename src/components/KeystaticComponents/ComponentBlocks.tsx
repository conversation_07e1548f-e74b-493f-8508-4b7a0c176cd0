import { fields } from "@keystatic/core";
import { wrapper } from "@keystatic/core/content-components";

// preview components
import KeystaticAdmonition from "./KeystaticAdmonition";

const Admonition = wrapper({
	label: "Admonition",
	ContentView: (props) => (
		<KeystaticAdmonition variant={props.value.variant}>{props.children as React.ReactNode}</KeystaticAdmonition>
	),
	schema: {
		variant: fields.select({
			label: "Variant",
			options: [
				{ value: "info", label: "Info" },
				{ value: "tip", label: "Tip" },
				{ value: "caution", label: "Caution" },
				{ value: "danger", label: "Danger" },
			],
			defaultValue: "info",
		}),
		// This makes it so you can edit what is inside the admonition
		content: fields.child({
			kind: "block",
			formatting: { inlineMarks: "inherit", softBreaks: "inherit" },
			links: "inherit",
			editIn: "both",
			label: "Admonition Content",
			placeholder: "Enter your admonition content here",
		}),
	},
});

export default {
	Admonition,
};