---
// utils

import { getAllPosts, countItems, sortByValue } from "@js/blogUtils";
import { humanize, slugify } from "@js/textUtils";
const posts = await getAllPosts("it");
const allCategories = posts.map((post) => post.data.categories).flat();
const countedCategories = countItems(allCategories);
const processedCategories = sortByValue(countedCategories);

interface Props {
	showCount?: boolean;
}

const { showCount = false } = Astro.props as Props;
---

<div class="flex flex-wrap justify-center gap-x-2 gap-y-2">
	{
		processedCategories.map(([category, count]) => (
			<a
				href={`/categories/${slugify(category)}/`}
				class="button text-base-400 hover:bg-primary-100 hover:text-primary-600 focus-visible:ring-primary-500 focus-visible:ring-opacity-50 dark:hover:bg-primary-500 px-4 py-1 font-semibold focus-visible:ring-2 dark:hover:text-white"
			>
				{humanize(category)}
				{showCount && <span class="text-xs opacity-70">{count}</span>}
			</a>
		))
	}
</div>