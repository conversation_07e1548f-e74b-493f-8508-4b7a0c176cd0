---
/**
 * * These are small cards with icons, titles, and text. With title first
 * This looks best with an even number of cards. They are smaller to
 * use with more cards in the same section. 4 or 6 cards is probably ideal
 *
 * ! I copy all icons into the src/icons folder and recommend you do the same
 * ! If the icon is at src/icons/tabler/paint.svg then you put in "tabler/paint"
 */

// components
import FeatureCard from "@components/FeatureCard/FeatureCardSmall.astro";
import Badge from "@components/Badge/Badge.astro";

interface FeatureData {
	icon: string;
	title: string;
	text: string;
}

// data
const featureData: FeatureData[] = [
	{
		icon: "tabler/hexagon-number-1",
		title: "<PERSON><PERSON><PERSON>",
		text: `Part<PERSON>o da te, con un'immersione profonda e oggettiva nella tua situazione attuale. Non ci basiamo su impressioni, ma su dati concreti: 
				Analisi della tua composizione corporea, dello stile di vita, delle abitudini alimentari e della tua storia clinica.
				Definizione degli obiettivi chiari, realistici e misurabili.`,
	},
	{
		icon: "tabler/hexagon-number-2",
		title: "Strategia Personalizzata",
		text: `<PERSON>ui, Nutrizione e Allenamento si fondono in un unico piano d'azione:
				Un piano nutrizionale sostenibile, che si integra nella tua vita e non viceversa.
				Un programma di allenamento sinergico, progressivo e sicuro, che potenzia i risultati della dieta.`,
	},
	{
		icon: "tabler/hexagon-number-3",
		title: "Monitoraggio Continuo",
		text: `Il tuo corpo è un sistema dinamico e il nostro piano deve esserlo altrettanto. Attraverso check-in periodici, monitoriamo i progressi in modo intelligente
		andando ad ottimizzare la strategia in tempo reale per superare gli ostacoli e massimizzare i risultati.`,
	},
	{
		icon: "tabler/hexagon-number-4",
		title: "Consolidamento e Autonomia",
		text: `Questo è il passo che fa la differenza e il mio vero obiettivo finale. Non voglio renderti dipendente da me, ma fornirti la conoscenza e gli strumenti per diventare l'esperto/a di te stesso/a.
				Imparerai il "perché" dietro ogni scelta nutrizionale e motoria.
				Svilupperai la consapevolezza per ascoltare i segnali del tuo corpo.
				Acquisirai un metodo che potrai usare per tutta la vita, per mantenere i risultati raggiunti e affrontare nuove sfide future.`,
	},
];
---

<section id="feature-cards-small" class="py-24 md:py-28">
	<div class="site-container relative">
		<!-- Badge-heading-description container with pattern background -->
		<div
			class="mx-auto mb-16 text-center md:max-w-4xl relative overflow-hidden bg-[url('/assets/pattern-light.svg')] bg-top bg-no-repeat dark:bg-[url('/assets/pattern-dark.svg')] py-8 rounded-2xl"
			data-aos="fade-up"
		>
			<!-- Content with proper z-index to appear above pattern -->
			<div class="relative z-10">
				<Badge>🔬 Metodo</Badge>
				<h2 class="h2 mb-4">Scienza, Dati e Personalizzazione</h2>
				<p class="description text-md text-pretty md:text-md">
					Un percorso scientifico in 4 fasi, disegnato per darti non solo risultati misurabili,
					ma anche la piena autonomia nella gestione del tuo benessere.
				</p>
			</div>
		</div>

		<!-- Feature cards container without background pattern -->
		<div class="mx-auto max-w-5xl">
			<div id="trigger-small-cards" class="grid gap-4 md:grid-cols-3 md:grid-rows-2">
			{
				featureData.map((feature, idx) => {
					// Layout asimmetrico come nell'immagine:
					// Prima riga: card larga (2 col) + card normale (1 col)
					// Seconda riga: card normale (1 col) + card larga (2 col)
					let gridClass = "";
					if (idx === 0) gridClass = "md:col-span-2"; // Prima card larga
					else if (idx === 1) gridClass = "md:col-span-1"; // Seconda card normale
					else if (idx === 2) gridClass = "md:col-span-1"; // Terza card normale
					else if (idx === 3) gridClass = "md:col-span-2"; // Quarta card larga
					else gridClass = "md:col-span-1"; // Altre card normali

					return (
						<FeatureCard
							title={feature.title}
							text={feature.text}
							icon={feature.icon}
							class={gridClass}
							data-aos="zoom-in"
							data-aos-delay={0.15 * idx}
							data-aos-trigger="#trigger-small-cards"
						/>
					);
				})
			}
			</div>
		</div>
	</div>
</section>