---
import { Icon } from "astro-icon/components";

interface Props {
	icon: string;
	social: string; // used for aria label
	href: string;
}

const { icon, social, href } = Astro.props as Props;
---

<a
	aria-label={social}
	href={href}
	target="_blank"
	rel="noopener noreferrer"
	class="text-base-400 hover:text-base-500 dark:hover:text-base-300 rounded-md p-2"
>
	<p class="sr-only">{social}</p>
	<Icon name={icon} class="size-8 transition" aria-hidden="true" />
</a>