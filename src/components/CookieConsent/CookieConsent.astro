---
/**
 * * A cookie consent banner for users to accept or decline cookies
 * Typically, you setup analytics to not use cookies by default, and update it if the user accepts cookies
 * You will need to update the javascript to match your analytics setup for accepting cookies
 *
 * Useful resources related to analytics / cookies:
 * - Setup GA4 and GTM: https://webreaper.dev/posts/astro-google-tag-manager-ga4/
 * - <PERSON><PERSON> consent with GA4: https://webreaper.dev/posts/google-tag-manager-cookie-consent/
 */

// components
import But<PERSON> from "@components/Button/Button.astro";


---

<div id="accept-banner" class="fixed inset-x-6 bottom-6 z-50 hidden justify-center">
	<div
		class="border-base-200 bg-base-50 flex flex-wrap items-center justify-center gap-x-6 gap-y-2 rounded-md border p-4"
	>
		<div class="text-center">
			<p class="text-base-500 text-sm font-medium lg:text-base">
				Utilizziamo i cookie per migliorare la tua esperienza su questo sito. Per saperne di più, consulta la nostra <a
					href={"/privacy-policy"}
					class="text-base-700 decoration-primary-500 hover:text-primary-500 underline transition"
					>Privacy Policy</a
				>.
			</p>
		</div>
		<div class="mx-auto flex gap-2">
			<Button variant="primary" type="button">Accetta</Button>
			<Button variant="ghost" type="button">Rifiuta</Button>
		</div>
	</div>
</div>

<script>
	// checks the "cookie-consent" cookie
	function getCookieConsent() {
		try {
			//@ts-ignore (it's in a try catch for a reason)
			const consent = document.cookie.match(/cookie-consent=([^;]+)/)[1];
			return consent;
		} catch (error) {
			return "unk";
		}
	}

	function cookieConsentSetup() {
		const cookieBanner = document.getElementById("accept-banner");
		const acceptButton = document.querySelector("#accept-banner button:first-of-type");
		const declineButton = document.querySelector("#accept-banner button:last-of-type");

		// Check if the user has already accepted or declined cookies
		const cookieConsent = getCookieConsent();

		// If the user has not already accepted or declined cookies, reveal the banner
		if (cookieConsent !== "accept" && cookieConsent !== "decline") {
			cookieBanner?.classList.add("flex");
			cookieBanner?.classList.remove("hidden");
		}

		// If the user clicks the accept button, hide the banner and set a cookie
		acceptButton?.addEventListener("click", () => {
			cookieBanner?.classList.add("hidden");
			// accepted cookie only lasts for the session
			document.cookie = "cookie-consent=accept; path=/";
			// example of cookies lasting for a year (might be limited by browser settings)
			// let d = new Date();
			// let oneYear = new Date(d.getFullYear() + 1, d.getMonth(), d.getDate());
			// document.cookie = "cookie-consent=granted; expires=" + oneYear + "; path=/";

			// call global function (head defined) to update GA4 or other analytics
			// Example here https://webreaper.dev/posts/google-tag-manager-cookie-consent/
			// consentGranted();
		});

		// If the user clicks the decline button, hide the banner and set a cookie
		declineButton?.addEventListener("click", () => {
			cookieBanner?.classList.add("hidden");
			// declined cookie only lasts for the session
			document.cookie = "cookie-consent=decline; path=/";
		});
	}

	// runs on initial page load
	cookieConsentSetup();

	// runs on view transitions navigation
	document.addEventListener("astro:after-swap", cookieConsentSetup);
</script>