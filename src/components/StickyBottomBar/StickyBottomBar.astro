---
// components
import <PERSON><PERSON> from "@components/Button/Button.astro";
---

<!-- Sticky Bottom Bar - Solo Mobile Homepage -->
<div class="sticky-bottom-bar fixed bottom-0 left-0 right-0 z-40 bg-white dark:bg-base-950 border-t border-base-200 dark:border-base-800 px-3 py-2 shadow-lg">
	<Button
		variant="primary"
		class="w-full text-base font-medium py-2.5"
		href="/contatti-e-prenotazioni"
	>
		Prenota Ora
	</Button>
</div>

<!-- Spacer per evitare che il contenuto venga coperto dalla bottom bar -->
<div class="sticky-bottom-spacer h-14"></div>

<style>
	/* Custom 848px breakpoint for sticky bottom bar */
	.sticky-bottom-bar {
		display: block;
	}

	.sticky-bottom-spacer {
		display: block;
	}

	@media (min-width: 848px) {
		.sticky-bottom-bar {
			display: none;
		}

		.sticky-bottom-spacer {
			display: none;
		}
	}
</style>
