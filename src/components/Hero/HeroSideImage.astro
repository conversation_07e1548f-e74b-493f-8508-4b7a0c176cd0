---
/**
 * * Hero with background blobs and side image
 *
 * There is a commented out "awards" section below in case you want that
 */
import { Image } from "astro:assets";

// components
import Button from "@components/Button/Button.astro";

// images
import heroImage from "@assets/images/place-for-photo.png";
---

<section
	id="hero-side"
	class="mt-20 overflow-clip bg-[url('/assets/pattern-light-big.svg')] bg-center bg-no-repeat dark:bg-[url('/assets/pattern-dark-big.svg')]"
>
	<div class="site-container mx-auto px-4 sm:px-5 md:px-6 lg:px-8 xl:px-10 pt-10 pb-20 md:py-28">
		<div class="-mx-4 flex flex-wrap xl:items-center">
			<div class="mb-16 w-full px-4 md:mb-0 md:w-1/2">
				<div data-aos="fade-right" data-aos-trigger="#hero-side">

					<h1
						class="text-base-900 dark:text-base-100 mb-6 text-3xl leading-tight font-bold tracking-tight text-pretty lg:text-5xl lg:leading-tight"
					>
						La Scienza per Far Funzionare di Nuovo il Tuo Corpo.
					</h1>
				</div>
				<p
					class="description mb-8 text-lg text-pretty lg:text-xl"
					data-aos="fade-right"
					data-aos-delay=".1"
					data-aos-trigger="#hero-side"
				>
					Dopo i 40 anni, il tuo corpo ha bisogno di un approccio più intelligente. 
					Scopri il metodo integrato di Nutrizione e Movimento basato sulla scienza, non sulle mode.
				</p>
				<div
					class="mt-6 flex flex-wrap gap-4"
					data-aos="fade-right"
					data-aos-delay=".2"
					data-aos-trigger="#hero-side"
				>
					{/**
					 * target="_blank" fa aprire il link in una nuova scheda.
					 * rel="noopener noreferrer" migliora la sicurezza:
					 *   - "noopener" impedisce che la nuova pagina acceda alla finestra che l'ha aperta.
					 *   - "noreferrer" impedisce di inviare il referrer al sito di destinazione.
					 * Usali sempre insieme per i link esterni che si aprono in una nuova scheda.
					 <Button
						variant="outline"
						href="https://cosmicthemes.com/themes/atlas/"
						target="_blank"
						rel="noopener noreferrer"
					>
						Get Atlas
					</Button>
					 */}
					<Button variant="primary" href={"/servizi"}>
						Inizia da qui 
					</Button>
					

				</div>


			</div>
			<div class="w-full px-4 md:w-1/2">
				<div
					class="relative mx-auto max-w-max md:mr-0"
					data-aos="fade-left"
					data-aos-trigger="#hero-side"
				>

					<div class="rounded-7xl relative overflow-hidden rounded-2xl">
						<Image
							src={heroImage}
							class="h-auto min-w-full object-cover"
							alt="small business owner"
							loading="eager"
							width={600}
							densities={[1.5, 2]}
						/>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<script>
	// Smooth scroll per il bottone "Scopri i Servizi"
	document.addEventListener('DOMContentLoaded', function() {
		// Trova tutti i link che puntano a sezioni interne
		const scrollLinks = document.querySelectorAll('a[href^="#"]');

		scrollLinks.forEach(link => {
			link.addEventListener('click', function(e) {
				e.preventDefault();

				const targetId = this.getAttribute('href').substring(1);
				const targetElement = document.getElementById(targetId);

				if (targetElement) {
					targetElement.scrollIntoView({
						behavior: 'smooth',
						block: 'start'
					});
				}
			});
		});
	});
</script>