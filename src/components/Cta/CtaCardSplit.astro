---
/**
 * * CTA split with text left and button on the right, with primary color background and light text
 */

// components
import Button from "@components/Button/Button.astro";
---

<section class="py-10">
	<div class="site-container md:px-10">
		<div class="bg-primary-500 relative overflow-hidden rounded-md px-18 py-11 md:py-20">


			<div class="relative z-20 -m-3 flex flex-wrap items-center">
				<div class="w-full p-3 md:w-1/2">
					<h2 class="mb-6 text-4xl font-bold tracking-tighter text-white">
						Pronto a creare un percorso su misura per te?
					</h2>
				</div>
				<div class="flex w-full p-3 md:w-1/2 md:justify-end">
					<!--
						target="_blank" apre il link in una nuova scheda.
						rel="noopener noreferrer" migliora la sicurezza:
						  - "noopener" impedisce che la nuova pagina acceda alla finestra che l'ha aperta.
						  - "noreferrer" impedisce di inviare il referrer al sito di destinazione.
						Usali insieme per i link esterni che si aprono in una nuova scheda.
					-->
					<Button
						variant="outline"
						href="/contatti-e-prenotazioni"
						class="shadow-none"
					>
						Prenota un appuntamento
					</Button>
				</div>
			</div>
		</div>
	</div>
</section>