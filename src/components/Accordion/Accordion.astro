---
import { Icon } from "astro-icon/components";

interface Props {
	title: string;
	details: string;
}

import { slugify } from "@js/textUtils";

const { title, details } = Astro.props as Props;
---

<div class="accordion group relative">
	<button
		class="primary-focus accordion__button text-base-900 hover:text-primary-500 dark:text-base-100 dark:hover:text-primary-500 flex w-full flex-1 items-center justify-between gap-2 py-3 text-left text-lg font-bold transition"
		type="button"
		id={`${slugify(title)}-accordion-menu-button`}
		aria-expanded="false"
		aria-controls={`${slugify(title)}-accordion-menu-content`}
	>
		{title}
		<Icon
			name="tabler/circle-arrow-up"
			aria-hidden="true"
			class="accordion__chevron text-primary-500 size-6 shrink-0 rotate-180 transition-transform"
		/>
	</button>

	<div
		id={`${slugify(title)}-accordion-menu-content`}
		aria-labelledby={`${slugify(title)}-accordion-menu-button`}
		class="accordion__content hidden max-h-0 overflow-hidden transition-all duration-300 ease-in-out"
	>
		<p
			class="description markdown-content mt-1 mr-auto mb-4 max-w-lg leading-snug transition-[height]"
		>
			<Fragment set:html={details} />
		</p>
	</div>
</div>

<script>
	function accordionSetup() {
		const accordionMenus = document.querySelectorAll(".accordion") as NodeListOf<HTMLElement>;
		accordionMenus.forEach((accordionMenu) => {
			const accordionButton = accordionMenu.querySelector(".accordion__button") as HTMLElement;
			const accordionChevron = accordionMenu.querySelector(".accordion__chevron") as HTMLElement;
			const accordionContent = accordionMenu.querySelector(".accordion__content") as HTMLElement;

			if (accordionButton && accordionContent && accordionChevron) {
				accordionButton.addEventListener("click", (event) => {
					if (!accordionMenu.classList.contains("active")) {
						// if accordion is currently closed, so open it
						accordionMenu.classList.add("active");
						accordionButton.setAttribute("aria-expanded", "true");

						// set max-height to the height of the accordion content
						// this makes it animate properly
						accordionContent.classList.remove("hidden");
						accordionContent.style.maxHeight = accordionContent.scrollHeight + "px";
						accordionChevron.classList.remove("rotate-180");

						// and close all other accordions
						accordionMenus.forEach((otherAccordionMenu) => {
							if (otherAccordionMenu !== accordionMenu) {
								otherAccordionMenu.classList.remove("active");
								otherAccordionMenu
									.querySelector(".accordion__button")
									?.setAttribute("aria-expanded", "false");
								otherAccordionMenu
									.querySelector(".accordion__content")
									?.setAttribute("style", "max-height: 0px");
								otherAccordionMenu
									.querySelector(".accordion__chevron")
									?.classList.add("rotate-180");
								setTimeout(() => {
									otherAccordionMenu.querySelector(".accordion__content")?.classList.add("hidden");
								}, 300);
							}
						});
					} else {
						// accordion is currently open, so close it
						accordionMenu.classList.remove("active");
						accordionButton.setAttribute("aria-expanded", "false");

						// set max-height to the height of the accordion content
						// this makes it animate properly
						accordionContent.style.maxHeight = "0px";
						accordionChevron.classList.add("rotate-180");
						// delay to allow close animation
						setTimeout(() => {
							accordionContent.classList.add("hidden");
						}, 300);
					}
					event.preventDefault();
					return false;
				});
			}
		});
	}

	// runs on initial page load
	accordionSetup();

	// runs on view transitions navigation
	document.addEventListener("astro:after-swap", accordionSetup);
</script>