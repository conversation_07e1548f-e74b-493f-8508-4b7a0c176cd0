/* buttons, 4 types "primary", "secondary", "outline", "ghost" */
.button {
	@apply flex items-center justify-center rounded-md px-7 py-3 font-medium transition-colors;
	@apply focus-visible:ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
	@apply disabled:pointer-events-none;
}

.button--primary {
	@apply bg-primary-500 text-white shadow-md;
	@apply hover:bg-primary-600;
	@apply focus-visible:ring-primary-500;
}

.button--secondary {
	@apply bg-base-500 text-base-50 shadow-sm;
	@apply hover:bg-base-600;
	@apply focus-visible:ring-base-500;
}

.button--outline {
	@apply border-base-200 bg-base-50 text-base-800 border shadow-sm;
	@apply hover:bg-base-100;
	@apply focus-visible:ring-base-200;
}

.button--ghost {
	@apply bg-base-50 text-base-800;
	@apply hover:bg-base-200;
	@apply focus-visible:ring-base-200;
}
