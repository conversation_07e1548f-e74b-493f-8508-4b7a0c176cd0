/**
 * * general styling for any markdown rendered content
 * 
 * Apply styling with the "markdown-content" class
 * Cancel out the styling with the "not-content" class
 */

/* text coloring for most items */
.markdown-content
	:not(pre *, .admonition *, details *, blockquote *, span, a, h1, h2, h3, h4, h5, h6):not(
		:where(.not-content *)
	) {
	@apply text-foreground/80;
}

/* paragraph spacing except for paragraphs inside list items */
.markdown-content p:not(li p):not(:where(.not-content *)) {
	@apply mt-2.5;
}

/* Headings after non-headings have more spacing. */
.markdown-content
	:not(h1, h2, h3, h4, h5, h6)
	+ :is(h1, h2, h3, h4, h5, h6):not(:where(.not-content *)) {
	@apply mt-10 mb-4;
}

.markdown-content :is(h1, h2, h3, h4, h5, h6):not(:where(.not-content *)) {
	@apply text-foreground/95 mt-2 mb-4 text-base leading-normal;
}

.markdown-content h1:not(:where(.not-content *)) {
	@apply text-3xl font-semibold md:text-4xl;
}
.markdown-content h2:not(:where(.not-content *)) {
	@apply text-2xl font-semibold md:text-3xl;
}
.markdown-content h3:not(:where(.not-content *)) {
	@apply text-xl font-semibold md:text-2xl;
}
.markdown-content h4:not(:where(.not-content *)) {
	@apply text-lg font-semibold md:text-xl;
}
.markdown-content h5:not(:where(.not-content *)) {
	@apply text-foreground/80 font-semibold md:text-lg;
}
.markdown-content h6:not(:where(.not-content *)) {
	@apply text-foreground/80 font-semibold md:text-lg;
}

/* anchor tags */
.markdown-content a:not(:where(.not-content *)) {
	@apply text-foreground/95 decoration-primary-500 dark:decoration-primary-600 hover:text-primary-500 dark:hover:text-primary-600 font-medium underline transition-colors;
}

/* code */
.markdown-content code:not(:where(.not-content *)) {
	@apply bg-base-100 dark:bg-base-800 border-base-100 dark:border-base-800 rounded-md border-2 border-solid px-[.1rem] py-0 font-mono;
}
.markdown-content :is(h1, h2, h3, h4, h5, h6) code {
	@apply text-inherit;
}

/* code block styling */
.markdown-content pre:not(:where(.not-content *)) {
	@apply my-4 rounded-lg px-4 py-3;
	tab-size: 2;
	&::-webkit-scrollbar {
		@apply h-1.5;
	}
	&::-webkit-scrollbar-track {
		@apply mx-1.5 bg-transparent;
	}
	&::-webkit-scrollbar-thumb {
		@apply bg-base-600 rounded-full;
	}
	&::-webkit-scrollbar-thumb:hover {
		@apply bg-base-500;
	}
}

/* reset styling for code blocks */
.markdown-content pre code:not(:where(.not-content *)) {
	all: unset;
	@apply font-mono;
}

/* blockquotes */
.markdown-content blockquote:not(:where(.not-content *)) {
	@apply border-primary-500 border-l-4 pl-4 font-medium italic;
}

/* images, videos, etc */
.markdown-content :is(img, picture, video, canvas, svg, iframe):not(:where(.not-content *)) {
	@apply my-4 block h-auto w-full rounded-lg;
}

/* italics */
.markdown-content em:not(:where(.not-content *)) {
	@apply text-foreground italic;
}

/* bold */
.markdown-content strong:not(:where(.not-content *)) {
	@apply text-foreground font-bold;
}

/* lists */
.markdown-content li + li:not(:where(.not-content *)),
.markdown-content dt + dt:not(:where(.not-content *)),
.markdown-content dt + dd:not(:where(.not-content *)),
.markdown-content dd + dd:not(:where(.not-content *)) {
	@apply mt-0.5;
}

.markdown-content li:not(:where(.not-content *)) {
	@apply pl-2;
	overflow-wrap: anywhere;
}

.markdown-content :is(ul, ol):not(:where(.not-content *)) {
	@apply my-4 mb-8 ml-6 list-outside space-y-2;
}

.markdown-content :is(ul):not(:where(.not-content *)) {
	@apply list-disc;
}

.markdown-content :is(ol):not(:where(.not-content *)) {
	@apply list-decimal;
	::marker {
		@apply text-foreground/60;
	}
}

.markdown-content dt:not(:where(.not-content *)) {
	@apply font-bold;
}
.markdown-content dd:not(:where(.not-content *)) {
	@apply ps-4;
}

/* Table styling */
.markdown-content table:not(:where(.not-content *)) {
	@apply mb-4 overflow-auto;
}
.markdown-content :where(table):not(:where(.not-content *)) {
	@apply w-full table-auto border-spacing-0 text-sm;
}
.markdown-content :is(th, td):not(:where(.not-content *)) {
	@apply border-border table-cell border-b px-4 py-2;
	/* Align text to the top of the row in multiline tables. */
	vertical-align: baseline;
}
.markdown-content :is(th:first-child, td:first-child):not(:where(.not-content *)) {
	padding-inline-start: 0;
}
.markdown-content :is(th:last-child, td:last-child):not(:where(.not-content *)) {
	padding-inline-end: 0;
}
.markdown-content th:not(:where(.not-content *)) {
	@apply text-foreground font-semibold;
}
/* Align headings to the start of the line unless set by the `align` attribute. */
.markdown-content th:not([align]):not(:where(.not-content *)) {
	text-align: start;
}

/* <table>s, and <hr>s inside admonitions */
.markdown-content .admonition :is(th, td, hr):not(:where(.not-content *)) {
	@apply border-border;
}

.markdown-content hr:not(:where(.not-content *)) {
	@apply border-b-border my-8 border-0 border-b-1;
}

/* <details> and <summary> styles */
.markdown-content details:not(:where(.not-content *)) {
	--details-border-color: var(--border);
	--details-border-color--hover: var(--primary);

	border-inline-start: 2px solid var(--details-border-color);
	padding-inline-start: 1rem;
}
.markdown-content details:not([open]):hover:not(:where(.not-content *)),
.markdown-content details:has(> summary:hover):not(:where(.not-content *)) {
	/* border-color: var(--details-border-color--hover); */
	@apply opacity-90;
}
.markdown-content summary:not(:where(.not-content *)) {
	@apply text-foreground block cursor-pointer font-bold;
	/* Expand the outline so that the marker cannot distort it. */
	margin-inline-start: -0.5rem;
	padding-inline-start: 0.5rem;
}
.markdown-content details[open] > summary:not(:where(.not-content *)) {
	margin-bottom: 1rem;
}

/* <summary> marker styles */
.markdown-content summary:not(:where(.not-content *))::marker,
.markdown-content summary:not(:where(.not-content *))::-webkit-details-marker {
	display: none;
}
.markdown-content summary:not(:where(.not-content *))::before {
	--details-marker-size: 1.25rem;

	background-color: currentColor;
	content: "";
	display: inline-block;
	height: var(--details-marker-size);
	width: var(--details-marker-size);
	margin-inline: calc((var(--details-marker-size) / 4) * -1) 0.25rem;
	vertical-align: middle;
	-webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M14.8 11.3 10.6 7a1 1 0 1 0-1.4 1.5l3.5 3.5-3.5 3.5a1 1 0 0 0 0 1.4 1 1 0 0 0 .7.3 1 1 0 0 0 .7-.3l4.2-4.2a1 1 0 0 0 0-1.4Z'/%3E%3C/svg%3E%0A");
	mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M14.8 11.3 10.6 7a1 1 0 1 0-1.4 1.5l3.5 3.5-3.5 3.5a1 1 0 0 0 0 1.4 1 1 0 0 0 .7.3 1 1 0 0 0 .7-.3l4.2-4.2a1 1 0 0 0 0-1.4Z'/%3E%3C/svg%3E%0A");
	-webkit-mask-repeat: no-repeat;
	mask-repeat: no-repeat;
}
@media (prefers-reduced-motion: no-preference) {
	.markdown-content summary:not(:where(.not-content *))::before {
		transition: transform 0.2s ease-in-out;
	}
}
.markdown-content details[open] > summary:not(:where(.not-content *))::before {
	transform: rotateZ(90deg);
}
[dir="rtl"] .markdown-content summary:not(:where(.not-content *))::before,
.markdown-content [dir="rtl"] summary:not(:where(.not-content *))::before {
	transform: rotateZ(180deg);
}
/* <summary> with only a paragraph automatically added when using MDX */
.markdown-content summary:not(:where(.not-content *)) p:only-child {
	display: inline;
}
