/**
 * * Tailwind theme settings
 * 
 * These are in a separate file so it can be imported into <style> tags in .astro components
 */
@theme {
	/* primary colors - <PERSON> (#006A71) palette */
	--color-primary-50: oklch(96.5% 0.015 200);
	--color-primary-100: oklch(92.8% 0.025 200);
	--color-primary-200: oklch(85.2% 0.045 200);
	--color-primary-300: oklch(76.8% 0.065 200);
	--color-primary-400: oklch(65.4% 0.085 200);
	--color-primary-500: oklch(54.2% 0.105 200);
	--color-primary-600: oklch(43.8% 0.095 200);
	--color-primary-700: oklch(35.6% 0.085 200);
	--color-primary-800: oklch(28.4% 0.075 200);
	--color-primary-900: oklch(22.8% 0.065 200);
	--color-primary-950: oklch(15.2% 0.045 200);

	/* base colors */
	--color-base-50: oklch(96.1% 0.0029 264.54);
	--color-base-100: oklch(94.51% 0.0034 247.86);
	--color-base-200: oklch(85.98% 0.0076 260.73);
	--color-base-300: oklch(79.65% 0.0169 245.14);
	--color-base-400: oklch(65.34% 0.0421 254.38);
	--color-base-500: oklch(51.65% 0.0536 258.33);
	--color-base-600: oklch(39.49% 0.038 258.88);
	--color-base-700: oklch(36.85% 0.035 258.34);
	--color-base-800: oklch(32.72% 0.0295 260.55);
	--color-base-900: oklch(28.68% 0.0248 258.35);
	--color-base-950: oklch(25.39% 0.0216 257.28);

	/* accents affect various SVG elements */
	--color-accent-1: var(--color-yellow-500);
	--color-accent-2: var(--color-blue-500);
	--color-accent-3: var(--color-purple-500);
	--color-accent-4: var(--color-red-500);

	/* other theme settings */
	--button-transition-properties:
		box-shadow, color, background-color, border-color, text-decoration-color, fill, stroke;

	/* layout settings */
	--breakpoint-xs: 400px;
	--breakpoint-sm: 640px;
	--breakpoint-md: 768px;
	--breakpoint-lg: 1024px;
	--breakpoint-xl: 1280px;
	--breakpoint-2xl: 1536px;

	/* font families */
	--font-fallback:
		"-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "Helvetica", "Arial", "sans-serif",
		"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
	--font-sans: "Poppins", var(--font-fallback);
	--font-serif:
		"Iowan Old Style", "Apple Garamond", "Baskerville", "Times New Roman", "Droid Serif", "Times",
		"Source Serif Pro", "serif", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
	--font-mono:
		"SFMono-Regular", "Menlo", "Monaco", "Consolas", "Liberation Mono", "Courier New", "monospace";

	/* starwind and cosmic themes animations */
	--animate-accordion-down: accordion-down 0.3s ease-out;
	--animate-accordion-up: accordion-up 0.3s ease-out;
	--animate-marquee: marquee 100s linear infinite;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--starwind-accordion-content-height);
		}
	}

	@keyframes accordion-up {
		from {
			height: var(--starwind-accordion-content-height);
		}
		to {
			height: 0;
		}
	}

	@keyframes marquee {
		from {
			transform: translateX(0);
		}
		to {
			transform: translateX(calc(-100% - 2.5rem));
		}
	}
}

/* "inline" option is necessary here https://github.com/tailwindlabs/tailwindcss/discussions/15122#discussioncomment-11356322 */
@theme inline {
	/* starwind utilities setup */
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-dark: var(--primary-dark);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-info: var(--info);
	--color-info-foreground: var(--info-foreground);
	--color-success: var(--success);
	--color-success-foreground: var(--success-foreground);
	--color-warning: var(--warning);
	--color-warning-foreground: var(--warning-foreground);
	--color-error: var(--error);
	--color-error-foreground: var(--error-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-outline: var(--outline);

	--radius-xs: calc(var(--radius) - 0.375rem);
	--radius-sm: calc(var(--radius) - 0.25rem);
	--radius-md: calc(var(--radius) - 0.125rem);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 0.25rem);
	--radius-2xl: calc(var(--radius) + 0.5rem);
	--radius-3xl: calc(var(--radius) + 1rem);
}
