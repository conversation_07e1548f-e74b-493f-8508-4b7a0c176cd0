@import url("./fonts.css");

@import "tailwindcss";

/* tailwind plugins */
@plugin "@tailwindcss/forms";
@plugin "tailwindcss-animate";

/* dark mode */
@variant dark (&:where(.dark, .dark *));

/* theme definition import */
@import "./tailwind-theme.css";

/* other imports into specific layers */
@import "./aos.css" layer(utilities);
@import "./markdown-content.css" layer(components);
@import "./buttons.css" layer(components);

/* https://tailwindcss.com/docs/adding-custom-styles#using-css-and-layer */
@layer base {
	:root {
		/* starwind component variables  */
		--background: oklch(97.87% 0.0017 247.84);
		--foreground: var(--color-base-900);
		--card: var(--color-base-50);
		--card-foreground: var(--color-base-950);
		--popover: var(--color-base-50);
		--popover-foreground: var(--color-base-950);
		--primary: var(--color-primary-700);
		--primary-dark: var(--color-primary-800);
		--primary-foreground: var(--color-base-50);
		--secondary: var(--foreground);
		--secondary-foreground: var(--background);
		--muted: var(--color-base-100);
		--muted-foreground: var(--color-base-600);
		--accent: var(--color-base-200);
		--accent-foreground: var(--color-base-900);
		--info: var(--color-sky-300);
		--info-foreground: var(--color-sky-950);
		--success: var(--color-primary-300);
		--success-foreground: var(--color-primary-950);
		--warning: var(--color-amber-300);
		--warning-foreground: var(--color-amber-950);
		--error: var(--color-red-700);
		--error-foreground: var(--color-base-50);
		--border: var(--color-base-200);
		--input: var(--color-base-200);
		--outline: var(--color-primary-500);

		--radius: 0.5rem;

		/* default font */
		font-family: var(--font-sans);
	}

	.dark {
		/* starwind dark theme config */
		--background: var(--color-base-950);
		--foreground: var(--color-base-100);
		--card: var(--color-base-950);
		--card-foreground: var(--color-base-100);
		--popover: var(--color-base-950);
		--popover-foreground: var(--color-base-100);
		--primary: var(--color-primary-500);
		--primary-dark: var(--color-primary-800);
		--primary-foreground: var(--color-base-50);
		--secondary: var(--foreground);
		--secondary-foreground: var(--background);
		--muted: var(--color-base-900);
		--muted-foreground: var(--color-base-400);
		--accent: var(--color-base-900);
		--accent-foreground: var(--color-base-100);
		--info: var(--color-sky-300);
		--info-foreground: var(--color-sky-950);
		--success: var(--color-primary-300);
		--success-foreground: var(--color-primary-950);
		--warning: var(--color-amber-300);
		--warning-foreground: var(--color-amber-950);
		--error: var(--color-red-400);
		--error-foreground: oklch(99% 0 0);
		--border: var(--color-base-700);
		--input: var(--color-base-800);
		--outline: var(--color-primary-500);

		@apply scheme-dark;
	}

	/* starwind preflight merged with Cosmic Themes */
	* {
		@apply border-border;
	}
	*:focus-visible {
		@apply outline-outline !transition-none;
	}
	html {
		@apply bg-background text-foreground leading-[1.6rem] scheme-light dark:scheme-dark;
		scroll-behavior: smooth;
		-moz-text-size-adjust: none;
		-webkit-text-size-adjust: none;
		text-size-adjust: none;
		/* Ensure full viewport coverage for mobile menu */
		width: 100%;
		overflow-x: hidden;
	}
	body {
		/* Prevent horizontal scroll that could cause backdrop issues */
		width: 100%;
		overflow-x: hidden;
	}
	button {
		@apply cursor-pointer;
	}
}

@layer components {
	.h1 {
		@apply text-base-900 dark:text-base-100 text-3xl leading-tight font-bold tracking-tight lg:text-5xl lg:leading-tight;
	}

	.h2 {
		@apply text-base-900 dark:text-base-100 text-3xl leading-tight font-bold tracking-tight md:text-4xl;
	}

	.h3 {
		@apply text-base-900 dark:text-base-100 text-xl leading-tight font-bold tracking-tight md:text-2xl;
	}

	.description {
		@apply text-base-500 dark:text-base-400 font-medium;
	}

	.site-container {
		@apply mx-auto max-w-[90rem] px-4;
	}

	/* Unified card base style for design system consistency */
	.card-base {
		@apply bg-white dark:bg-base-800 rounded-xl shadow-lg border border-base-200 dark:border-base-700 h-full;
	}

	/* Card hover states for different interaction patterns */
	.card-hover-subtle {
		@apply transition duration-200 hover:shadow-xl;
	}

	.card-hover-interactive {
		@apply transition-all duration-300 hover:shadow-xl hover:scale-[1.02];
	}

	/* used for newsletter, and contact form inputs */
	.form__input {
		@apply bg-base-100 text-base-900 w-full rounded-md border border-transparent p-2.5 transition;
		@apply dark:bg-base-800 dark:text-base-200 dark:focus-visible:border-primary-500 dark:focus-visible:ring-primary-500;
		@apply focus-visible:border-primary-500/50 focus-visible:ring-primary-500/50 focus-visible:ring-2;
	}

	/* styles navbar links */
	.nav__link--base {
		@apply text-base-500 hover:text-base-600 dark:text-base-400 dark:hover:text-base-300 leading-tight font-medium transition;
	}
}

@layer utilities {
	/* used with select field */
	.sr-only {
		@apply sr-only;
	}

	/* focus utility class (for when user is tabbing through items on page) */
	.primary-focus {
		@apply focus-visible:outline-primary-500 focus-visible:rounded-xs focus-visible:outline-2;
	}

	.rotate-180 {
		@apply rotate-180;
	}

	/* use with "animate-pause" to pause a CSS animation */
	.pause {
		animation-play-state: paused !important;
	}
}
