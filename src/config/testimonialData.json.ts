import { type TestimonialItem } from "./types/configDataTypes";

import PlaceholderImage from "@images/place-for-photo.png";

export const testimonialData: TestimonialItem[] = [
	{
		avatar: PlaceholderImage,
		name: "<PERSON>",
		title: "Impren<PERSON><PERSON>",
		testimonial: `Finalmente ho trovato l'equilibrio perfetto tra alimentazione e allenamento. I risultati sono arrivati in poche settimane!`,
	},
	{
		avatar: PlaceholderImage,
		name: "<PERSON>iu<PERSON>",
		title: "Insegnante",
		testimonial: `L'approccio integrato di Emanuele ha risolto i miei problemi digestivi. Mi sento rinata e piena di energia.`,
	},
	{
		avatar: PlaceholderImage,
		name: "<PERSON>",
		title: "Manager",
		testimonial: `Professionalità e competenza eccezionali. Il piano personalizzato ha trasformato il mio stile di vita.`,
	},
	{
		avatar: PlaceholderImage,
		name: "<PERSON>",
		title: "Mamma e Libera Professionista",
		testimonial: `Do<PERSON> la gravidanza pensavo fosse impossibile tornare in forma. Emanuele mi ha dimostrato il contrario!`,
	},
	{
		avatar: PlaceholderImage,
		name: "<PERSON> <PERSON>",
		title: "Atleta Amatoriale",
		testimonial: `Le mie performance sono migliorate drasticamente. Finalmente capisco cosa significa nutrizione sportiva.`,
	},
	{
		avatar: PlaceholderImage,
		name: "Chiara Romano",
		title: "Studentessa Universitaria",
		testimonial: `Approccio scientifico e umano insieme. Mi ha aiutata a superare le mie difficoltà con l'alimentazione.`,
	},
];

export default testimonialData;
