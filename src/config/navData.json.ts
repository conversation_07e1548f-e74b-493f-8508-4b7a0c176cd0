/**
 * * This file is used to define the navigation links for the site.
 * Notes:
 * 1 level of dropdown is supported
 * Mega menus look best with 3-5 columns, but supports anything > 2 columns
 * If using icons, the icon should be saved in the src/icons folder. If filename is "tabler/icon.svg" then input "tabler/icon"
 * Recommend getting icons from https://tabler.io/icons
 */

// utils
import { getAllPosts, countItems, sortByValue } from "@js/blogUtils";
import { humanize } from "@js/textUtils";

// get the categories used in blog posts, to put into navbar
// const posts = await getAllPosts("it");
// const allCategories = posts.map((post) => post.data.categories).flat();
// const countedCategories = countItems(allCategories);
// const processedCategories = sortByValue(countedCategories);

// types
import { type navItem } from "./types/configDataTypes";

// note: 1 level of dropdown is supported
const navConfig: navItem[] = [

	{
		text: "Home",
		link: "/",
	},

	{
		text: "Chi Sono",
		link: "/chi-sono",
	},
	
	// regular dropdown
	{
		text: "Servizi",
		dropdown: [
			{
				text: "Ricomposizione Corporea",
				link: "/servizi/ricomposizione-corporea-e-gestione-del-peso/",
			},
			{
				text: "Salute Metabolica e Cardiovascolare",
				link: "/servizi/salute-metabolica-e-cardiovascolare/",
			},
			{
				text: "Benessere Intestinale",
				link: "/servizi/benessere-intestinale/",
			},
			{
				text: "Nutrizione Sportiva Over 40",
				link: "/servizi/nutrizione-sportiva-over-40/",
			},
			{
				text: "Nutrizione e Menopausa",
				link: "/servizi/nutrizione-e-menopausa/",
			},
			{
				text: "Forza e Vitalità Over 60",
				link: "/servizi/forza-e-vitalita-over-60/",
			},
		],
	},

	// {
	// 	text: "Risorse",
	// 	link: "/risorse",
	// },
	
	{
		text: "Blog",
		link: "/blog",
	},
	

	// {
	//   // get the categories used in blog posts, to put into a navbar dropdown
	//   text: "Categories",
	//   dropdown: processedCategories.map(([category, count]) => {
	//     return {
	//       text: humanize(category),
	//       link: `/categories/${category}`,
	//     };
	//   }),
	// },
];

export default navConfig;
