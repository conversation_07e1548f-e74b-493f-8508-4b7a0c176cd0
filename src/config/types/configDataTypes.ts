// site data types
export interface SiteDataProps {
	name: string;
	title: string;
	description: string;
	contact: {
		// used for contact page and footer
		address1: string; // contact address (line 1)
		address2: string; // contact address (line 2)
		phone: string; // contact phone number
		email: string; // contact email address
	};
	author: {
		// used for blog post purposes
		name: string;
		email: string;
		twitter: string; // used for twitter cards when sharing a blog post on twitter
	};
	defaultImage: {
		src: string;
		alt: string;
	};
}

// --------------------------------------------------------
// nav data types
export interface navLinkItem {
	text: string;
	link: string;
	newTab?: boolean; // adds target="_blank" rel="noopener noreferrer" to link
	icon?: string; // adds an icon to the left of the text
}

export interface navDropdownItem {
	text: string;
	dropdown: navLinkItem[];
}

export interface navMegaDropdownColumn {
	title: string;
	items: navLinkItem[];
}

export interface navMegaDropdownItem {
	text: string;
	megaMenuColumns: navMegaDropdownColumn[];
}

export type navItem = navLinkItem | navDropdownItem | navMegaDropdownItem;

// --------------------------------------------------------
// faq data types
export interface FaqItem {
	question: string; // this is the question of the accordion
	answer: string; // these are the details seen after expanding the accordion
}

// --------------------------------------------------------
// testimonial data types
export interface TestimonialItem {
	avatar: ImageMetadata; // an imported image
	name: string;
	title: string;
	testimonial: string;
}

// --------------------------------------------------------
// team data types
export interface teamMember {
	image: ImageMetadata; // an imported image
	name: string;
	title: string;
	// la bio e seo sono stati aggiunti dopo che la pagina chi-sono e' stata convertita 
	// in MDX e il teamdata.json.ts è stato modificato con le aggiunte di keywords, location etc. Valutare se farlo anche con le altre pagine
	bio?: string; // optional fallback for non-MDX content
	// SEO-specific fields
	seo?: {
		description: string; // meta description
		keywords: string[]; // relevant keywords
		location: string; // primary location
		specializations: string[]; // main specializations
	};
}

// --------------------------------------------------------
// site settings types
export interface SiteSettingsProps {
	useViewTransitions?: boolean;
	useAnimations?: boolean;
}