import { type SiteDataProps } from "./types/configDataTypes";

// Update this file with your site specific information
const siteData: SiteDataProps = {
	name: "<PERSON><PERSON>. <PERSON><PERSON> - Biologo Nutrizionista e Chinesiologo",
	// Your website's title and description (meta fields)
	title: "Dott. Emanuel<PERSON> | Nutrizione e Movimento per Over 40",
	description:
		"La tua guida scientifica per la salute over 40. Il Dott. Emanuel<PERSON>, Nutrizionista e Chinesiologo, crea percorsi su misura per il tuo benessere.",

	// used on contact page and footer
	contact: {
		address1: "Via XXV Aprile 1/L",
		address2: "Todi, PG 06059",
		phone: "+************",
		email: "<EMAIL>",
	},

	// Your information for blog post purposes
	author: {
		name: "<PERSON><PERSON>. <PERSON><PERSON>",
		email: "<EMAIL>",
		twitter: "dott_belloni", // Update with actual Twitter handle
	},

	// default image for meta tags if the page doesn't have an image already
	defaultImage: {
		src: "/images/dott-emanuele-belloni.png", // Update with actual image
		alt: "Dott. Emanuele Belloni - Biologo Nutrizionista e Chinesiologo",
	},
};

export default siteData;
