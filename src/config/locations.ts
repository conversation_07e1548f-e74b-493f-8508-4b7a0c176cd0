/**
 * Multi-Location Configuration for SEO and Business Information
 * 
 * This file manages all location-specific data for the practice.
 * When adding new studio locations, add them to the LOCATIONS object
 * and update the LocationKey type accordingly.
 */

export interface LocationData {
	key: string;
	city: string;
	province: string;
	region: string;
	coordinates: {
		lat: number;
		lng: number;
	};
	postalCode: string;
	address?: {
		street: string;
		number: string;
	};
	phone?: string;
	isActive: boolean;
	isPrimary: boolean;
}

// Type-safe location keys
export type LocationKey = 'bastiaUmbra' | 'online';

// Location configuration object
export const LOCATIONS: Record<LocationKey, LocationData> = {
	bastiaUmbra: {
		key: 'bastiaUmbra',
		city: 'Bastia Umbra',
		province: 'Perugia',
		region: 'Umbria',
		coordinates: {
			lat: 43.0707,
			lng: 12.5511
		},
		postalCode: '06083',
		address: {
			street: 'Via Sacco e Vanzett',
			number: '1'
		},
		phone: '+393349070084',
		isActive: true,
		isPrimary: true
	},
	online: {
		key: 'online',
		city: 'Online',
		province: 'Nazionale',
		region: 'Italia',
		coordinates: {
			lat: 42.8333, // Centro Italia
			lng: 12.8333
		},
		postalCode: '00000',
		isActive: true,
		isPrimary: false
	}
} as const;

// Helper functions
export function getActiveLocations(): LocationData[] {
	return Object.values(LOCATIONS).filter(location => location.isActive);
}

export function getPrimaryLocation(): LocationData {
	const primary = Object.values(LOCATIONS).find(location => location.isPrimary);
	if (!primary) {
		throw new Error('No primary location configured');
	}
	return primary;
}

export function getLocationByKey(key: LocationKey): LocationData | undefined {
	return LOCATIONS[key];
}

// SEO-specific helpers
export function getLocationSeoData(location: LocationData) {
	return {
		geoRegion: `IT-${location.province === 'Perugia' ? 'PG' : location.province.substring(0, 2).toUpperCase()}`,
		geoPlacename: location.city === 'Online' ? location.province : location.city,
		geoPosition: `${location.coordinates.lat};${location.coordinates.lng}`,
		icbm: `${location.coordinates.lat}, ${location.coordinates.lng}`
	};
}

export function getLocationDisplayName(location: LocationData): string {
	if (location.city === 'Online') {
		return 'Consulenze Online';
	}
	return `${location.city}, ${location.province}`;
}

// Business schema helpers
export function getLocationBusinessSchema(location: LocationData) {
	if (location.city === 'Online') {
		return {
			'@type': 'ProfessionalService',
			name: 'Dott. Emanuele Belloni - Consulenze Online',
			serviceType: 'Consulenza Nutrizionale Online',
			areaServed: {
				'@type': 'Country',
				name: 'Italia'
			}
		};
	}

	return {
		'@type': 'MedicalBusiness',
		name: `Dott. Emanuele Belloni - Studio ${location.city}`,
		address: {
			'@type': 'PostalAddress',
			streetAddress: location.address ? `${location.address.street} ${location.address.number}` : '',
			addressLocality: location.city,
			addressRegion: location.region,
			postalCode: location.postalCode,
			addressCountry: 'IT'
		},
		telephone: location.phone,
		areaServed: [
			{
				'@type': 'City',
				name: location.city,
				containedInPlace: `${location.province}, Italia`
			}
		]
	};
}
