---
import { Icon } from "astro-icon/components";

// main layout
import BaseLayout from "@layouts/BaseLayout.astro";

// components
import MultiChannelBooking from "@components/Forms/MultiChannelBooking.astro";
import Badge from "@components/Badge/Badge.astro";

// data
import siteData from "@config/siteData.json.ts";
---

<BaseLayout title="Prenota una Consulenza | Dott. Emanuele <PERSON>" 
			description="Pronto/a a iniziare? Fissa qui la tua prima consulenza con il Dott. Belloni. Facciamo insieme il primo passo verso il tuo benessere."
>
	<section class="site-container">
		<div
			class="overflow-x-clip bg-[url('/assets/pattern-light.svg')] bg-top bg-no-repeat pt-24 md:pt-32 dark:bg-[url('/assets/pattern-dark-big.svg')]"
		>
			<div class="mx-auto flex max-w-[950px] flex-col justify-center">
				<div class="mx-auto">
					<Badge>📅 Prenotazioni</Badge>
				</div>
				<h1 class="h1 text-center">
					Prenota la tua Consulenza
				</h1>
				<p class="description text-center mx-auto mt-6 max-w-2xl">
					Scegli il metodo di prenotazione che preferisci.
					Il calendario integrato per un'esperienza veloce e personalizzata oppure tramite piattaforme esterne se già registrato.
				</p>
			</div>

			<!-- multi-channel booking interface -->
			<div class="mt-8 flex pb-8 md:mt-8">
				<div class="w-full mb-25">
					<MultiChannelBooking />
				</div>
			</div>
		</div>

		<!-- Contact info - positioned horizontally below form -->
		<div class="mb-0 mt-8 grid gap-6 md:grid-cols-3">
			<!-- Email -->
			<!-- <div class="flex flex-col items-center gap-3">
				<div
					class="bg-primary-500 inline-flex size-12 items-center justify-center overflow-hidden rounded-full"
				>
					<Icon name="tabler/mail" class="size-6 text-white" />
				</div>
				<h3 class="h3">Email</h3>
				<a href={`mailto:${siteData.contact.email}`} class="description hover:text-base-600 transition text-center"
					>{siteData.contact.email}</a
				>
			</div> -->

			<!-- Phone -->
			<!-- <div class="flex flex-col items-center gap-3">
				<div
					class="bg-primary-500 inline-flex size-12 items-center justify-center overflow-hidden rounded-full"
				>
					<Icon name="tabler/phone" class="size-6 text-white" />
				</div>
				<h3 class="h3">WhatsApp</h3>
				<a
					href={`https://wa.me/${siteData.contact.phone.replace(/\D/g, '')}?text=${encodeURIComponent('Ciao, vorrei prenotare una consulenza nutrizionale.')}`}
					class="description hover:text-base-600 transition text-center"
					target="_blank"
					rel="noopener noreferrer"
				>{siteData.contact.phone}</a>
			</div> -->

			<!-- Address -->
			<!-- <div class="flex flex-col items-center gap-3">
				<div
					class="bg-primary-500 inline-flex size-12 items-center justify-center overflow-hidden rounded-full"
				>
					<Icon name="tabler/map-pin" class="size-6 text-white" />
				</div>
				<h3 class="h3">Studio</h3>
				<button
					id="address-button"
					class="description flex flex-col items-center text-center hover:text-base-600 transition cursor-pointer bg-transparent border-none p-0"
					data-address1={siteData.contact.address1}
					data-address2={siteData.contact.address2}
				>
					<p>{siteData.contact.address1}</p>
					<p>{siteData.contact.address2}</p>
				</button>
			</div> -->
		</div>
		
	</section>
</BaseLayout>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		const addressButton = document.getElementById('address-button');

		if (addressButton) {
			addressButton.addEventListener('click', function() {
				const address1 = this.getAttribute('data-address1');
				const address2 = this.getAttribute('data-address2');
				const fullAddress = `${address1}, ${address2}`;
				const encodedAddress = encodeURIComponent(fullAddress);

				// Rileva il dispositivo e il browser
				const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
				const isAndroid = /Android/.test(navigator.userAgent);
				const isMacOS = /Macintosh|MacIntel|MacPPC|Mac68K/.test(navigator.userAgent);
				const isMobile = isIOS || isAndroid;

				function tryNativeApp(nativeUrl, fallbackUrl) {
					// Crea un iframe nascosto per provare l'app nativa senza errori visibili
					const iframe = document.createElement('iframe');
					iframe.style.display = 'none';
					iframe.src = nativeUrl;
					document.body.appendChild(iframe);

					// Rimuovi l'iframe dopo un breve periodo
					setTimeout(() => {
						document.body.removeChild(iframe);
					}, 100);

					// Fallback dopo un breve ritardo
					setTimeout(() => {
						window.open(fallbackUrl, '_blank');
					}, 300);
				}

				if (isIOS) {
					// Su iOS, prova Apple Maps
					const appleUrl = `maps://maps.apple.com/?q=${encodedAddress}`;
					const fallbackUrl = `https://maps.google.com/maps?q=${encodedAddress}`;
					tryNativeApp(appleUrl, fallbackUrl);

				} else if (isAndroid) {
					// Su Android, prova Google Maps
					const geoUrl = `geo:0,0?q=${encodedAddress}`;
					const fallbackUrl = `https://maps.google.com/maps?q=${encodedAddress}`;
					tryNativeApp(geoUrl, fallbackUrl);

				} else if (isMacOS) {
					// Su macOS, prova prima Apple Maps, poi Google Maps
					const appleUrl = `maps://?q=${encodedAddress}`;
					const fallbackUrl = `https://maps.google.com/maps?q=${encodedAddress}`;
					tryNativeApp(appleUrl, fallbackUrl);

				} else {
					// Su altri desktop, apri direttamente Google Maps
					window.open(`https://maps.google.com/maps?q=${encodedAddress}`, '_blank');
				}
			});
		}
	});
</script>
