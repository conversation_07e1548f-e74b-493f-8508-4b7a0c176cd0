---
import { type InferGetStaticPropsType, type InferGetStaticParamsType } from "astro";

// main layout
import BaseLayout from "@layouts/BaseLayout.astro";

// components
import CategoryCloud from "@components/CategoryCloud/CategoryCloud.astro";
import Badge from "@components/Badge/Badge.astro";
import PostCard from "@components/PostCard/PostCardAtlas.astro";

// utils
import { getAllPosts, countItems, sortByValue } from "@js/blogUtils";
import { slugify, humanize } from "@js/textUtils";



export const prerender = true;

// generate pagination for tag pages if there are a bunch of posts for a single tag
export async function getStaticPaths() {
	const posts = await getAllPosts("it");
	const allCategories = posts.map((post) => post.data.categories).flat();
	const countedCategories = countItems(allCategories);
	const processedCategories = sortByValue(countedCategories);

	return processedCategories.map(([category, count]) => ({
		params: { category: category },
	}));
}

type Props = InferGetStaticPropsType<typeof getStaticPaths>;
type Params = InferGetStaticParamsType<typeof getStaticPaths>;

// const filteredPosts = Astro.props as Props;
const { category } = Astro.params as Params;

const posts = await getAllPosts("it");
const filteredPosts = posts.filter((post) => {
	// make sure to slugify them for comparison
	const slugifiedCategory = post.data.categories.map((category) => slugify(category));

	return slugifiedCategory.includes(category);
});

// Dati per il sistema SEO centralizzato
const categoryDisplayName = humanize(category);
const postCount = filteredPosts.length;
---

<BaseLayout
	type="category"
	title={`Articoli e Guide: ${categoryDisplayName} | Dott. Emanuele Belloni`}
	description={`Scopri ${postCount} articoli, guide, approfondimenti scientifici e consigli pratici su ${categoryDisplayName}.`}
	category={category}
	postCount={postCount}
>
	<!-- SEO: Meta aggiuntivi specifici per categorie -->
	<meta name="author" content="Dott. Emanuele Belloni" slot="head" />
	<meta name="keywords" content={`${categoryDisplayName.toLowerCase()}, nutrizione, biologo nutrizionista, chinesiologo, Todi, ${category.replace(/-/g, ', ')}`} slot="head" />
	<meta name="geo.region" content="IT-PG" slot="head" />
	<meta name="geo.placename" content="Todi" slot="head" />
	<meta name="geo.position" content="42.7818;12.4058" slot="head" />
	<meta name="ICBM" content="42.7818, 12.4058" slot="head" />


	<section class="site-container">
		<div
			class="overflow-x-clip bg-[url('/assets/pattern-light.svg')] bg-center bg-no-repeat pt-24 md:pt-32 dark:bg-[url('/assets/pattern-dark.svg')]"
		>
			<div class="mx-auto flex max-w-[950px] flex-col justify-center">
				<div class="mx-auto">
					<Badge>📂 Blog</Badge>
				</div>
				<h1 class="h1 text-center">
					Articoli su: <span class="decoration-primary-500 underline"
						>{humanize(category).toLowerCase()}</span
					>
				</h1>
			</div>

			<div class="mt-20 flex pb-8 md:mt-28">
				<CategoryCloud />
			</div>
		</div>

		<div class="grid gap-8 gap-y-10 md:grid-cols-2">
			{filteredPosts.map((post) => <PostCard post={post} showDescription={true} />)}
		</div>
	</section>
</BaseLayout>