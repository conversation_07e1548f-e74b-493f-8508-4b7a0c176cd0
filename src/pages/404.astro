---
import { Image } from "astro:assets";

// main layout
import BaseLayout from "@layouts/BaseLayout.astro";

// components
import Button from "@components/Button/Button.astro";

// images
import errorImage from "@assets/images/place-for-photo.png";
---

<BaseLayout title="404 - Pagina Non Trovata" description="Errore 404 - La pagina che stai cercando non esiste." noindex={true}>
	<section
		class="min-h-[80vh] flex items-center justify-center bg-[url('/assets/pattern-light-big.svg')] bg-center bg-no-repeat py-24 dark:bg-[url('/assets/pattern-dark-big.svg')]"
	>
		<div class="site-container mx-auto px-4">
			<div class="flex flex-col items-center justify-center text-center max-w-4xl mx-auto">
				<!-- Immagine del cane -->
				<div class="mb-12">
					<Image
						src={errorImage}
						alt="Errore 404"
						class="mx-auto max-w-sm h-auto"
						width={400}
						height={300}
					/>
				</div>

				<!-- Contenuto testuale centrato -->
				<div class="max-w-2xl">
					<span
						class="bg-primary-100 text-primary-500 dark:bg-primary-500 mb-6 inline-block rounded-full px-4 py-2 text-sm leading-5 font-medium shadow-sm dark:text-white"
						>⚠️ Errore 404</span
					>
					<h1
						class="text-base-900 dark:text-base-100 mb-6 text-4xl leading-tight font-bold tracking-tighter md:text-6xl"
					>
						Oops! Pagina Non Trovata
					</h1>
					<p class="text-base-500 mb-8 text-lg md:text-xl max-w-xl mx-auto">
						La pagina che stai cercando non esiste o è stata spostata. Non preoccuparti, succede anche ai migliori!
					</p>
					<div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
						<Button variant="primary" href={"/"}>
							Torna alla Home
						</Button>
						
					</div>
				</div>
			</div>
		</div>
	</section>
</BaseLayout>