---
import { type CollectionEntry, getCollection, render } from "astro:content";

// layout
import BaseLayout from "@layouts/BaseLayout.astro";

// components
import ExternalLink from "@components/MarkdownComponents/ExternalLink.astro";



export const prerender = true;

export async function getStaticPaths() {
	const otherPages: CollectionEntry<"otherPages">[] = await getCollection(
		"otherPages",
		({ data }) => {
			// filter out draft pages
			return data.draft !== true;
		},
	);

	// Filter pages by Italian language and remove locale from slug
	const filteredPages = otherPages.filter((page) => page.id.startsWith("it/"));

	return filteredPages.map((page) => ({
		params: { page: page.id.replace("it/", "") },
		props: page,
	}));
}

type Props = CollectionEntry<"otherPages">;
const page = Astro.props as Props;

// convert markdown to html
const { Content } = await render(page);
const { title, description } = page.data;
---

<BaseLayout title={title} description={description}>
	<section class="site-container mt-16 overflow-x-clip">
		<div
			class="w-full bg-[url('/assets/pattern-light.svg')] bg-center bg-no-repeat py-20 text-center md:py-32 dark:bg-[url('/assets/pattern-dark.svg')]"
		>
			<h1 class="h1">{title}</h1>
		</div>
		<div class="mx-auto max-w-3xl">
			<div class="text-base-content markdown-content mt-10 max-w-none text-sm md:text-base">
				<Content components={{ a: ExternalLink }} />
			</div>
		</div>
	</section>
</BaseLayout>