---
import { type CollectionEntry, getCollection, render } from "astro:content";

// layout
import ServiceLayoutCenter from "@layouts/ServiceLayoutCenter.astro";

// components
import ExternalLink from "@components/MarkdownComponents/ExternalLink.astro";



export const prerender = true;

export async function getStaticPaths() {
	const services = await getCollection("servizi", ({ data }) => {
		// filter out draft services
		return data.draft !== true;
	});

	// Filter services by Italian language and remove locale from slug
	const filteredServices = services.filter((service) => service.id.startsWith("it/"));

	return filteredServices.map((service) => ({
		params: { slug: service.id.replace("it/", "") },
		props: service,
	}));
}

const service = Astro.props as CollectionEntry<"servizi">;
const { Content } = await render(service);

// Extract service slug from the service id (remove "it/" prefix)
const serviceSlug = service.id.replace("it/", "");
---

<ServiceLayoutCenter
	title={service.data.title}
	description={service.data.description}
	image={service.data.image}
	serviceSlug={serviceSlug}
>
	<Content components={{ a: ExternalLink }} />
</ServiceLayoutCenter>