---
// layout
import BaseLayout from "@layouts/BaseLayout.astro";

// components
import Badge from "@components/Badge/Badge.astro";
import ServiceCard from "@components/ServiceCard/ServiceCardIcon.astro";
import CtaCardSplit from "@components/Cta/CtaCardSplit.astro";

// data
import { servicesData } from "@/data/servicesData";

export const prerender = true;
---

<BaseLayout
	title="Percorsi Nutrizionali su Misura | Dott. Emanuele Belloni"
	description="Dal benessere intestinale alla performance sportiva. Soluzioni scientifiche che uniscono nutrizione e movimento per i tuoi specifici obiettivi di salute e forma fisica."
>
	<!-- Hero Section -->
	<section class="site-container">
		<div
			class="overflow-x-clip bg-[url('/assets/pattern-light-big.svg')] bg-center bg-no-repeat pt-24 md:pt-32 dark:bg-[url('/assets/pattern-dark-big.svg')]"
		>
			<div class="mx-auto flex max-w-[950px] flex-col justify-center">
				<div class="mx-auto">
					<Badge>📌 Servizi</Badge>
				</div>
				<h1 class="h1 text-center mb-6">
					Nutrizione e Movimento: la Scienza per i Tuoi Obiettivi
				</h1>
				<p class="description text-center text-lg md:text-xl max-w-4xl mx-auto">
					Ogni percorso è costruito su misura a partire da dati oggettivi e finalizzato a un unico scopo: il raggiungimento concreto e misurabile del tuo traguardo.
					Che la tua meta sia migliorare la tua salute, la tua forma fisica o la tua performance, qui troverai la via per raggiungerla.
				</p>
			</div>
		</div>
	</section>

	<!-- Services Grid -->
	<section class="py-24 md:py-28">
		<div class="mx-auto max-w-[85rem] px-8 md:px-12 lg:px-16">
			<div class="grid gap-4 sm:grid-cols-2 md:grid-cols-3" id="services-grid">
				{
					servicesData.map((service, idx) => (
						<ServiceCard
							title={service.title}
							text={service.text}
							icon={service.icon}
							href={service.href}
							idx={idx}
							data-aos="zoom-in"
							data-aos-delay={0.15 * idx}
							data-aos-trigger="#services-grid"
						/>
					))
				}
			</div>
		</div>
	</section>



	<!-- CTA Section -->
	<CtaCardSplit />
</BaseLayout>