---
/**
 * * About page with various sections including some text, team profiles, and CTA
 * Edit the team member information inside src/config/teamData.json.ts
 */
import { Icon } from "astro-icon/components";

// main layout
import BaseLayout from "@layouts/BaseLayout.astro";

// components
import Profile from "@components/Profile/Profile.astro";
import Button from "@components/Button/Button.astro";
import CtaCardSplit from "@components/Cta/CtaCardSplit.astro";
import Badge from "@components/Badge/Badge.astro";

// data
import teamData from "@config/teamData.json.ts";
import CtaCardSplitChisono from "@components/Cta/CtaCardSplit-chisono.astro";
---

<BaseLayout 
			
			title="Chi Sono | Dott. Emanuele Belloni, Nutrizionista e Chinesiologo" 
			description="Stanco/a di soluzioni che non funzionano? Scopri perché il mio approccio scientifico integrato di nutrizione e movimento è la risposta per la tua salute.">

	<section class="site-container">
		<div
			class="overflow-x-clip bg-[url('/assets/pattern-light-big.svg')] bg-top bg-no-repeat pt-24 md:pt-32 dark:bg-[url('/assets/pattern-dark-big.svg')]"
		>
			<div class="mx-auto flex max-w-[950px] flex-col justify-center">
				<div class="mx-auto">
					<Badge>🤝 Chi Sono</Badge>
				</div>
				<!-- <h1 class="h1 text-center">
					"Unire la scienza della nutrizione e la forza del movimento per costruire il tuo benessere dopo i 40 anni.
				</h1> -->
			</div>

			<!-- Profile info - Full width layout -->
			<div class="mt-8 flex pb-8 md:mt-8">
				<div class="w-full flex flex-col gap-12">
					{
						teamData.map((member) => (
							<Profile
								name={member.name}
								title={member.title}
								image={member.image}
								bio={member.bio}
								seo={member.seo}
								useMdx={true}
							/>
						))
					}
				</div>
			</div>
		</div>
	</section>
	<CtaCardSplitChisono />
</BaseLayout>