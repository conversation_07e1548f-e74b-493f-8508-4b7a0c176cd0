---
// layout
import BaseLayout from "@layouts/BaseLayout.astro";

// components
import HeroSideImage from "@components/Hero/HeroSideImage.astro";
import FeatureCardsSmall from "@components/Feature/FeatureCardsSmall.astro";
import ServicesIcon from "@components/Services/ServicesIcon.astro";
import TestimonialsSwiperQuotes from "@components/Testimonials/TestimonialsSwiperQuotes.astro";
import CtaCardSplit from "@components/Cta/CtaCardSplit.astro";
import FaqAccordions from "@components/Faq/FaqAccordions.astro";
import StickyBottomBar from "@components/StickyBottomBar/StickyBottomBar.astro";

// data
import siteData from "@config/siteData.json.ts";
---

<BaseLayout title={siteData.title} description={siteData.description}>

	<HeroSideImage />

	<ServicesIcon />

	<FeatureCardsSmall />
	
	<TestimonialsSwiperQuotes />

	<FaqAccordions />

	<CtaCardSplit />

	<StickyBottomBar />
	
</BaseLayout>