---
// layout
import BaseLayout from "@layouts/BaseLayout.astro";

// components
import Button from "@components/Button/Button.astro";
import { Icon } from "astro-icon/components";

// data
import siteData from "@config/siteData.json.ts";
---

<BaseLayout
	title="Prenotazione Confermata"
	description="Perfetto! Abbiamo ricevuto la sua richiesta di prenotazione. A breve riceverà un'email di conferma con tutti i dettagli." 
	noindex={true}
>
	<section class="mx-auto max-w-6xl px-4 py-16 sm:px-6 lg:px-8">
		<div class="mx-auto max-w-3xl text-center bg-[url('/assets/pattern-light-big.svg')] bg-top bg-no-repeat py-24">

			<!-- Success Icon -->
			<!-- <div class="mx-auto mb-8 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
				<svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
				</svg>
			</div> -->

			<!-- Thank You Message -->
			<h1 class="mb-4 text-4xl font-bold text-base-900 sm:text-5xl">
				Grazie! 🎉
			</h1>
			
			<p class="mb-8 text-xl text-base-600">
				Abbiamo ricevuto la tua prenotazione.
			</p>

			<div class="mb-12 rounded-lg bg-base-50 p-6">
				<h2 class="mb-4 text-lg font-semibold text-base-900">
					Ecco cosa accade ora 
				</h2>
				<ul class="space-y-2 text-left text-base-600">
					<li class="flex items-start">
						<span class="mr-2 text-primary-500">•</span>
						Riceverai a breve un'email con il riepilogo completo. (Consiglio: controlla la cartella Spam).
					</li>
					<li class="flex items-start">
						<span class="mr-2 text-primary-500">•</span>
						Esaminerò la tua prenotazione con la massima attenzione.
					</li>
					<li class="flex items-start">
						<span class="mr-2 text-primary-500">•</span>
						Ti contatteremo solo qualora avessimo bisogno di confermare qualche dettaglio per garantirti il miglior servizio
					</li>
				</ul>
			</div>

			<!-- Contact Info -->
			<div class="mb-8 text-base-600">
				<p class="mb-4">
					Per domande urgenti, scrivici su WhatsApp.
				</p>
				<div class="flex justify-center">
					<a
						href={`https://wa.me/${siteData.contact.phone.replace(/\D/g, '')}?text=${encodeURIComponent('Ciao! Hai qualche domanda o dubbio? Scrivimi pure, sono qui per aiutarti! 😊')}`}
						class="text-primary-600 hover:text-primary-700 transition-colors inline-flex items-center gap-2"
						target="_blank"
						rel="noopener noreferrer"
					>
						<Icon name="tabler/brand-whatsapp" class="size-10" aria-hidden="true" />
						<span class="sr-only">{siteData.contact.phone}</span>
					</a>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex flex-col gap-4 sm:flex-row sm:justify-center">
				<!-- <Button href="/" variant="primary">
					Torna alla Home
				</Button> -->

				<Button href="/blog" variant="primary">
					Scopri il mio Blog
				</Button>
			</div>
		</div>
	</section>

</BaseLayout>


