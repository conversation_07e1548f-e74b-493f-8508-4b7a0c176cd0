---
// ⚡ PERFORMANCE: CSS-Optimized Layout
import BaseLayout from "@layouts/BaseLayout.astro";

// components
import Badge from "@components/Badge/Badge.astro";
import IntegratedBooking from "@components/Forms/IntegratedBooking.astro";
---

<BaseLayout title="Prenotazione Integrata | Dott. Emanuel<PERSON>" description="Prenota la tua visita nutrizionale con il nostro sistema integrato. Seleziona sede, tipo di appuntamento e trova l'orario perfetto per te.">
	<!-- ⚡ PERFORMANCE: Critical CSS Inline - Above the Fold Only -->
	<style slot="head">
		/* Critical CSS for immediate rendering - Above the fold content */
		.site-container {
			margin-left: auto;
			margin-right: auto;
			max-width: 90rem;
			padding-left: 1rem;
			padding-right: 1rem;
		}

		.h1 {
			color: rgb(23 23 23);
			font-size: 1.875rem;
			line-height: 1.1;
			font-weight: 700;
			letter-spacing: -0.025em;
		}

		@media (min-width: 1024px) {
			.h1 {
				font-size: 3rem;
				line-height: 1.1;
			}
		}

		.description {
			color: rgb(107 114 128);
			font-weight: 500;
		}

		/* Critical layout classes */
		.overflow-x-clip { overflow-x: clip; }
		.bg-top { background-position: top; }
		.bg-no-repeat { background-repeat: no-repeat; }
		.pt-24 { padding-top: 6rem; }
		.mx-auto { margin-left: auto; margin-right: auto; }
		.flex { display: flex; }
		.max-w-\[950px\] { max-width: 950px; }
		.flex-col { flex-direction: column; }
		.justify-center { justify-content: center; }
		.text-center { text-align: center; }
		.mt-6 { margin-top: 1.5rem; }
		.max-w-2xl { max-width: 42rem; }
		.mt-8 { margin-top: 2rem; }
		.pb-8 { padding-bottom: 2rem; }
		.w-full { width: 100%; }
		.mb-25 { margin-bottom: 6.25rem; }

		@media (min-width: 768px) {
			.md\:pt-32 { padding-top: 8rem; }
			.md\:mt-8 { margin-top: 2rem; }
		}

		/* Critical background patterns - inline to prevent FOUC */
		.bg-pattern-light {
			background-image: url('/assets/pattern-light.svg');
		}

		/* Prevent layout shift for form elements */
		.form-container {
			min-height: 400px;
		}

		/* Critical loading state to prevent CLS */
		#calendly-container {
			min-height: 500px;
			border-radius: 0.5rem;
			border: 1px solid rgb(229 231 235);
		}
	</style>

	<!-- ⚡ PERFORMANCE: Preload critical resources -->
	<link slot="head" rel="preload" href="/assets/pattern-light.svg" as="image" type="image/svg+xml">

	<!-- ⚡ PERFORMANCE: DNS prefetch for external resources -->
	<link slot="head" rel="dns-prefetch" href="//calendly.com">
	<link slot="head" rel="dns-prefetch" href="//assets.calendly.com">
	<link slot="head" rel="preconnect" href="https://calendly.com" crossorigin>
	<link slot="head" rel="preconnect" href="https://assets.calendly.com" crossorigin>

	<!-- ⚡ PERFORMANCE: Smart CSS Loading Strategy -->
	<script slot="head" is:inline>
		// Intelligent CSS loading based on user interaction
		let nonCriticalLoaded = false;
		let calendlyPreloaded = false;

		function loadNonCriticalCSS() {
			if (nonCriticalLoaded) return;
			nonCriticalLoaded = true;

			const link = document.createElement('link');
			link.rel = 'stylesheet';
			link.href = '/styles/non-critical.css';
			link.media = 'print';
			link.onload = function() { this.media = 'all'; };
			document.head.appendChild(link);
		}

		function preloadCalendlyCSS() {
			if (calendlyPreloaded) return;
			calendlyPreloaded = true;

			// Preload Calendly CSS when user starts interacting with form
			const preloadLink = document.createElement('link');
			preloadLink.rel = 'preload';
			preloadLink.href = 'https://assets.calendly.com/assets/external/widget.css';
			preloadLink.as = 'style';
			preloadLink.crossOrigin = 'anonymous';
			document.head.appendChild(preloadLink);
		}

		// Load non-critical CSS after initial render
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', () => {
				setTimeout(loadNonCriticalCSS, 100);
			});
		} else {
			setTimeout(loadNonCriticalCSS, 100);
		}

		// Preload Calendly resources on first form interaction
		document.addEventListener('DOMContentLoaded', () => {
			const formSelects = document.querySelectorAll('select[data-select]');
			formSelects.forEach(select => {
				select.addEventListener('focus', preloadCalendlyCSS, { once: true });
				select.addEventListener('mouseenter', preloadCalendlyCSS, { once: true });
			});
		});

		// ⚡ PERFORMANCE: Monitor Core Web Vitals
		if ('PerformanceObserver' in window) {
			// Monitor LCP (Largest Contentful Paint)
			new PerformanceObserver((list) => {
				for (const entry of list.getEntries()) {
					console.log('LCP:', entry.startTime);
				}
			}).observe({ entryTypes: ['largest-contentful-paint'] });

			// Monitor CLS (Cumulative Layout Shift)
			new PerformanceObserver((list) => {
				let clsValue = 0;
				for (const entry of list.getEntries()) {
					if (!entry.hadRecentInput) {
						clsValue += entry.value;
					}
				}
				console.log('CLS:', clsValue);
			}).observe({ entryTypes: ['layout-shift'] });

			// Monitor FCP (First Contentful Paint)
			new PerformanceObserver((list) => {
				for (const entry of list.getEntries()) {
					console.log('FCP:', entry.startTime);
				}
			}).observe({ entryTypes: ['paint'] });
		}
	</script>
	<section class="site-container">
		<div class="overflow-x-clip bg-pattern-light bg-top bg-no-repeat pt-24 md:pt-32">
			<div class="mx-auto flex max-w-[950px] flex-col justify-center">
				<div class="mx-auto">
					<Badge>📅 Prenotazione</Badge>
				</div>
				<h1 class="h1 text-center">
					Prenota la Tua Visita
				</h1>
				<p class="description text-center mx-auto mt-6 mb-8 max-w-2xl">
				</p>

			</div>

			<!-- Integrated Booking Component -->
			<div class="mt-8 flex pb-8 md:mt-8">
				<div class="w-full mb-25">
					<IntegratedBooking />
				</div>
			</div>
		</div>
	</section>
</BaseLayout>


