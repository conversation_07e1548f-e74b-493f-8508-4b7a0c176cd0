<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   version="1.1"
   id="svg1"
   width="720.66699"
   height="533.33331"
   viewBox="0 0 720.667 533.33331"
   sodipodi:docname="Dott..ai"
   inkscape:export-batch-path="../logo"
   inkscape:export-batch-name="Dott."
   inkscape:version="0.0"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <defs
     id="defs1">
    <rect
       x="68.502434"
       y="351.07495"
       width="575.13501"
       height="125.58779"
       id="rect717" />
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath2">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-146.2134,-232.34711)"
         id="path2" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath4">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-170.7534,-221.83541)"
         id="path4" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath6">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-174.2187,-218.61181)"
         id="path6" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath8">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-181.36911,-218.61181)"
         id="path8" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath10">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path10" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath12">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-145.7026,-164.78461)"
         id="path12" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath14">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path14" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath16">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-167.6895,-163.5229)"
         id="path16" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath18">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path18" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath20">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-183.5034,-163.5229)"
         id="path20" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath22">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-193.37841,-164.852)"
         id="path22" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath24">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-204.11131,-163.5229)"
         id="path24" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath26">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-214.42531,-159.4868)"
         id="path26" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath28">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-227.17681,-162.1264)"
         id="path28" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath30">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-238.01071,-161.5385)"
         id="path30" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath32">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-245.1772,-159.4868)"
         id="path32" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath34">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path34" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath36">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-258.36721,-159.4868)"
         id="path36" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath38">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path38" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath40">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-279.7651,-163.5229)"
         id="path40" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath42">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-284.49271,-159.4868)"
         id="path42" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath44">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path44" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath46">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-299.63431,-161.63911)"
         id="path46" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath48">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-309.92921,-161.5385)"
         id="path48" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath50">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-319.11521,-160.24261)"
         id="path50" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath52">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-326.90481,-158.6626)"
         id="path52" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath54">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-337.06541,-165.1382)"
         id="path54" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath56">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-350.72561,-159.4868)"
         id="path56" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath58">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path58" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath60">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-366.45511,-159.4868)"
         id="path60" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath62">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-377.64261,-164.0786)"
         id="path62" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath64">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-386.74411,-161.63911)"
         id="path64" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath66">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path66" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath68">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-407.83941,-163.5229)"
         id="path68" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath70">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path70" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath72">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-423.65331,-163.5229)"
         id="path72" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath74">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-433.52831,-164.852)"
         id="path74" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath76">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-444.26171,-163.5229)"
         id="path76" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath78">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-144.8784,-178.4966)"
         id="path78" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath80">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-168.00831,-178.4966)"
         id="path80" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath82">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-209.68951,-180.66451)"
         id="path82" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath84">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-225.39751,-178.4966)"
         id="path84" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath86">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-247.85251,-186.062)"
         id="path86" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath88">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-272.3311,-191.6528)"
         id="path88" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath90">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path90" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath92">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-301.67631,-191.6528)"
         id="path92" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath94">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-334.34671,-193.1948)"
         id="path94" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath96">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-360.27001,-192.2309)"
         id="path96" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath98">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path98" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath100">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path100" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath102">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-411.54051,-190.1596)"
         id="path102" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath104">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-418.14061,-178.4966)"
         id="path104" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath106">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         id="path106" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath108">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-92.111305,-193.9477)"
         id="path108" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath110">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-57.221201,-196.9702)"
         id="path110" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath112">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-83.694805,-239.84321)"
         id="path112" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath114">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-65.281301,-234.0542)"
         id="path114" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath116">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-90.049802,-240.81391)"
         id="path116" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath118">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-75.708999,-227.9819)"
         id="path118" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath120">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-71.813502,-238.20461)"
         id="path120" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath122">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-71.389202,-235.7915)"
         id="path122" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath124">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-65.613302,-236.06101)"
         id="path124" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath126">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-88.670905,-241.02491)"
         id="path126" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath128">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-87.9004,-239.59621)"
         id="path128" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath130">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-84.325705,-240.97121)"
         id="path130" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath132">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-106.6821,-154.13521)"
         id="path132" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath134">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-110.4951,-232.10981)"
         id="path134" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath136">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-122.665,-168.43411)"
         id="path136" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath138">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-96.8125,-218.15281)"
         id="path138" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath140">
      <path
         d="M 0,400 H 500 V 0 H 0 Z"
         transform="translate(-98.919902,-198.7915)"
         id="path140" />
    </clipPath>
  </defs>
  <sodipodi:namedview
     id="namedview1"
     pagecolor="#ffffff"
     bordercolor="#000000"
     borderopacity="0.25"
     inkscape:showpageshadow="2"
     inkscape:pageopacity="0.0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#d1d1d1"
     inkscape:zoom="0.73309764"
     inkscape:cx="802.07597"
     inkscape:cy="280.31737"
     inkscape:window-width="1440"
     inkscape:window-height="900"
     inkscape:window-x="0"
     inkscape:window-y="0"
     inkscape:window-maximized="0"
     inkscape:current-layer="layer-MC0">
    <inkscape:page
       x="0"
       y="0"
       inkscape:label="1"
       id="page1"
       width="720.66699"
       height="533.33331"
       margin="197.28799 72.837334 197.28799 72.837196"
       bleed="0"
       inkscape:export-filename="logo.svg"
       inkscape:export-xdpi="96"
       inkscape:export-ydpi="96" />
  </sodipodi:namedview>
  <g
     id="layer-MC0"
     inkscape:groupmode="layer"
     inkscape:label="Layer 1">
    <path
       id="path1"
       d="m 0,0 v -15.821 h 4.172 c 4.822,0 7.342,2.6 7.342,7.911 0,5.309 -2.52,7.91 -7.342,7.91 z m -1.3,-16.986 v 18.15 h 5.634 c 5.663,0 8.507,-3.332 8.507,-9.074 0,-5.744 -2.844,-9.076 -8.507,-9.076 z"
       style="fill:#6e6e6e;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(0.88272625,0,0,-0.88027153,207.04145,219.14254)"
       clip-path="url(#clipPath2)" />
    <path
       id="path3"
       d="m 0,0 c 0,3.576 -1.896,5.635 -4.687,5.635 -2.79,0 -4.686,-2.059 -4.686,-5.635 0,-3.549 1.896,-5.635 4.686,-5.635 C -1.896,-5.635 0,-3.549 0,0 m -10.701,0 c 0,4.091 2.465,6.745 6.014,6.745 3.549,0 6.014,-2.654 6.014,-6.745 0,-4.091 -2.465,-6.745 -6.014,-6.745 -3.549,0 -6.014,2.654 -6.014,6.745"
       style="fill:#6e6e6e;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(0.88272625,0,0,-0.88027153,228.70355,228.39568)"
       clip-path="url(#clipPath4)" />
    <path
       id="path5"
       d="M 0,0 V 8.642 H -2.141 V 9.698 H 0 v 2.005 l 1.3,0.976 V 9.698 H 5.716 V 8.642 H 1.3 V 0.298 c 0,-1.978 1.084,-2.601 2.357,-2.601 1.138,0 2.222,0.759 2.709,1.734 L 6.854,-1.842 C 6.285,-2.817 4.958,-3.521 3.44,-3.521 1.571,-3.521 0,-2.411 0,0"
       style="fill:#6e6e6e;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(0.88272625,0,0,-0.88027153,231.76247,231.23332)"
       clip-path="url(#clipPath6)" />
    <path
       id="path7"
       d="M 0,0 V 8.642 H -2.14 V 9.698 H 0 v 2.005 l 1.3,0.976 V 9.698 H 5.716 V 8.642 H 1.3 V 0.298 c 0,-1.978 1.084,-2.601 2.358,-2.601 1.137,0 2.221,0.759 2.709,1.734 L 6.854,-1.842 C 6.285,-2.817 4.958,-3.521 3.44,-3.521 1.571,-3.521 0,-2.411 0,0"
       style="fill:#6e6e6e;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(0.88272625,0,0,-0.88027153,238.07432,231.23332)"
       clip-path="url(#clipPath8)" />
    <path
       id="path9"
       d="m 188.844,218.287 h 1.517 v -2.926 h -1.517 z"
       style="fill:#6e6e6e;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(0.88272625,0,0,-0.88027153,77.975041,423.67108)"
       clip-path="url(#clipPath10)" />
    <path
       id="path11"
       d="m 0,0 v -4.322 h 3.246 c 1.9,0 2.859,0.638 2.859,2.169 C 6.105,-0.639 4.811,0 3.078,0 Z M 0,4.995 V 0.976 h 2.91 c 1.581,0 2.691,0.47 2.691,2.002 0,1.546 -1.11,2.017 -2.641,2.017 z M -1.11,-5.298 V 5.971 h 4.423 c 2.288,0 3.415,-1.143 3.415,-2.86 0,-1.177 -0.69,-2.32 -2.708,-2.54 2.389,-0.252 3.212,-1.446 3.212,-2.859 0,-1.9 -1.362,-3.01 -3.801,-3.01 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,208.33796,312.83912)"
       clip-path="url(#clipPath12)" />
    <path
       id="path13"
       d="m 156.637,167.56 h 1.11 v -8.073 h -1.11 z m 0,3.195 h 1.11 v -1.749 h -1.11 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,-39.918108,622.35695)"
       clip-path="url(#clipPath14)" />
    <path
       id="path15"
       d="m 0,0 c 0,2.153 -1.077,3.296 -2.675,3.296 -1.581,0 -2.658,-1.143 -2.658,-3.296 0,-2.136 1.077,-3.264 2.658,-3.264 C -1.077,-3.264 0,-2.136 0,0 m -6.442,0 c 0,2.557 1.547,4.206 3.767,4.206 2.221,0 3.785,-1.649 3.785,-4.206 0,-2.557 -1.564,-4.205 -3.785,-4.205 -2.22,0 -3.767,1.648 -3.767,4.205"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,245.80043,315.65029)"
       clip-path="url(#clipPath16)" />
    <path
       id="path17"
       d="m 172.451,170.755 h 1.11 v -11.269 h -1.11 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,-39.918108,622.35695)"
       clip-path="url(#clipPath18)" />
    <path
       id="path19"
       d="m 0,0 c 0,2.153 -1.077,3.296 -2.675,3.296 -1.58,0 -2.657,-1.143 -2.657,-3.296 0,-2.136 1.077,-3.264 2.657,-3.264 C -1.077,-3.264 0,-2.136 0,0 m -6.442,0 c 0,2.557 1.547,4.206 3.767,4.206 2.221,0 3.785,-1.649 3.785,-4.206 0,-2.557 -1.564,-4.205 -3.785,-4.205 -2.22,0 -3.767,1.648 -3.767,4.205"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,272.74503,315.65029)"
       clip-path="url(#clipPath20)" />
    <path
       id="path21"
       d="m 0,0 c 0,1.228 -0.858,2.019 -2.069,2.019 -1.195,0 -2.052,-0.791 -2.052,-2.019 0,-1.228 0.857,-2.019 2.052,-2.019 C -0.858,-2.019 0,-1.228 0,0 m 0.689,-6.224 c 0,0.639 -0.454,0.924 -1.412,0.924 h -3.28 c -0.404,-0.251 -0.841,-0.722 -0.841,-1.243 0,-0.791 0.891,-1.245 2.422,-1.245 1.951,0 3.111,0.64 3.111,1.564 m -4.406,3.718 c -0.252,-0.304 -0.505,-0.723 -0.505,-1.111 0,-0.454 0.269,-0.723 1.143,-0.723 h 2.507 c 1.413,0 2.287,-0.571 2.287,-1.749 0,-1.497 -1.547,-2.574 -4.137,-2.574 -2.053,0 -3.33,0.69 -3.33,1.8 0,0.841 0.807,1.362 1.446,1.615 -0.555,0.151 -0.824,0.538 -0.824,1.025 0,0.657 0.505,1.329 1.177,1.851 -0.774,0.505 -1.245,1.346 -1.245,2.372 0,1.682 1.262,2.877 3.129,2.877 0.589,0 1.111,-0.119 1.565,-0.32 L 0.151,4.508 1.312,4.07 -0.135,2.338 C 0.623,1.816 1.06,0.992 1.06,0 c 0,-1.682 -1.245,-2.877 -3.129,-2.877 -0.622,0 -1.177,0.135 -1.648,0.371"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,289.57059,313.05643)"
       clip-path="url(#clipPath22)" />
    <path
       id="path23"
       d="m 0,0 c 0,2.153 -1.076,3.296 -2.674,3.296 -1.581,0 -2.658,-1.143 -2.658,-3.296 0,-2.136 1.077,-3.264 2.658,-3.264 C -1.076,-3.264 0,-2.136 0,0 m -6.442,0 c 0,2.557 1.547,4.206 3.768,4.206 2.22,0 3.784,-1.649 3.784,-4.206 0,-2.557 -1.564,-4.205 -3.784,-4.205 -2.221,0 -3.768,1.648 -3.768,4.205"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,307.85789,315.65029)"
       clip-path="url(#clipPath24)" />
    <path
       id="path25"
       d="M 0,0 V 11.269 H 0.992 L 6.493,3.263 7.468,1.765 7.401,3.229 v 8.04 h 1.11 V 0 H 7.519 L 1.867,8.207 1.042,9.452 1.11,8.19 V 0 Z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,325.43144,322.79008)"
       clip-path="url(#clipPath26)" />
    <path
       id="path27"
       d="M 0,0 V 5.434 H 1.093 V 0.236 c 0,-1.296 0.606,-2.103 1.986,-2.103 1.749,0 2.421,1.228 2.421,2.641 v 4.66 H 6.594 V -2.64 H 5.5 v 2.303 C 5.214,-1.683 4.306,-2.809 2.641,-2.809 1.06,-2.809 0,-1.867 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,347.15815,318.37571)"
       clip-path="url(#clipPath28)" />
    <path
       id="path29"
       d="M 0,0 V 5.163 H -1.312 V 6.021 H 0 V 7.183 L 1.094,8.006 V 6.021 H 3.802 V 5.163 H 1.094 v -4.86 c 0,-1.161 0.605,-1.482 1.312,-1.482 0.723,0 1.311,0.522 1.581,1.095 L 4.374,-1.16 C 4.021,-1.75 3.229,-2.237 2.187,-2.237 0.959,-2.237 0,-1.464 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,365.61754,319.52307)"
       clip-path="url(#clipPath30)" />
    <path
       id="path31"
       d="M 0,0 V 8.073 H 1.11 V 5.869 C 1.413,7.147 2.305,8.259 3.684,8.259 4.625,8.259 5.4,7.652 5.4,6.543 5.4,5.937 5.248,5.432 5.097,5.163 H 3.902 C 4.121,5.415 4.323,5.937 4.323,6.424 4.323,6.946 4.021,7.417 3.28,7.417 2.069,7.417 1.11,6.071 1.11,4.676 V 0 Z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,377.82821,323.52717)"
       clip-path="url(#clipPath32)" />
    <path
       id="path33"
       d="m 253.774,167.56 h 1.11 v -8.073 h -1.11 z m 0,3.195 h 1.11 v -1.749 h -1.11 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,-39.918108,622.35695)"
       clip-path="url(#clipPath34)" />
    <path
       id="path35"
       d="M 0,0 V 0.873 L 4.693,6.374 5.433,7.147 4.222,7.098 H 0.336 V 8.073 H 6.711 V 7.198 L 2.018,1.765 1.261,0.924 2.607,0.976 H 6.879 V 0 Z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,400.30206,323.52717)"
       clip-path="url(#clipPath36)" />
    <path
       id="path37"
       d="m 268.713,167.56 h 1.11 v -8.073 h -1.11 z m 0,3.195 h 1.11 v -1.749 h -1.11 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,-39.918108,622.35695)"
       clip-path="url(#clipPath38)" />
    <path
       id="path39"
       d="m 0,0 c 0,2.153 -1.076,3.296 -2.675,3.296 -1.58,0 -2.657,-1.143 -2.657,-3.296 0,-2.136 1.077,-3.264 2.657,-3.264 C -1.076,-3.264 0,-2.136 0,0 m -6.442,0 c 0,2.557 1.547,4.206 3.767,4.206 2.221,0 3.785,-1.649 3.785,-4.206 0,-2.557 -1.564,-4.205 -3.785,-4.205 -2.22,0 -3.767,1.648 -3.767,4.205"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,436.76096,315.65029)"
       clip-path="url(#clipPath40)" />
    <path
       id="path41"
       d="M 0,0 V 8.073 H 1.11 V 5.819 c 0.286,1.312 1.212,2.423 2.827,2.423 1.598,0 2.674,-0.943 2.674,-2.794 L 6.611,0 H 5.5 V 5.197 C 5.5,6.509 4.895,7.315 3.499,7.315 1.8,7.315 1.11,6.071 1.11,4.676 V 0 Z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,444.8161,323.52717)"
       clip-path="url(#clipPath42)" />
    <path
       id="path43"
       d="m 295.075,167.56 h 1.11 v -8.073 h -1.11 z m 0,3.195 h 1.11 v -1.749 h -1.11 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,-39.918108,622.35695)"
       clip-path="url(#clipPath44)" />
    <path
       id="path45"
       d="M 0,0 0.825,0.623 C 1.076,-0.472 2.086,-1.38 3.717,-1.38 4.794,-1.38 5.5,-0.942 5.5,-0.101 5.5,0.74 4.777,1.042 3.684,1.295 L 2.59,1.548 C 1.43,1.833 0.235,2.371 0.235,3.817 c 0,1.398 1.111,2.273 2.776,2.273 1.715,0 2.843,-0.859 3.246,-2.003 L 5.416,3.465 C 5.046,4.559 4.205,5.163 2.944,5.163 1.968,5.163 1.278,4.709 1.278,3.869 1.278,3.027 1.884,2.725 3.061,2.439 L 4.138,2.17 C 5.82,1.749 6.493,1.061 6.493,-0.084 6.493,-1.631 5.181,-2.321 3.684,-2.321 2.035,-2.321 0.488,-1.581 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,470.61519,319.32673)"
       clip-path="url(#clipPath46)" />
    <path
       id="path47"
       d="M 0,0 V 5.163 H -1.312 V 6.021 H 0 V 7.183 L 1.094,8.006 V 6.021 H 3.802 V 5.163 H 1.094 v -4.86 c 0,-1.161 0.605,-1.482 1.312,-1.482 0.723,0 1.311,0.522 1.581,1.095 L 4.374,-1.16 C 4.021,-1.75 3.229,-2.237 2.187,-2.237 0.959,-2.237 0,-1.464 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,488.15622,319.52307)"
       clip-path="url(#clipPath48)" />
    <path
       id="path49"
       d="m 0,0 c 1.497,0 2.607,1.161 2.607,2.254 v 1.43 L -0.017,2.438 C -0.892,2.036 -1.295,1.632 -1.295,1.026 -1.295,0.354 -0.791,0 0,0 m -2.405,0.875 c 0,1.06 0.79,1.732 1.917,2.237 l 3.061,1.396 v 0.067 c 0,1.212 -0.572,1.918 -1.884,1.918 -1.21,0 -2.085,-0.689 -2.506,-1.581 L -2.59,5.687 c 0.504,0.839 1.681,1.799 3.398,1.799 1.883,0 2.859,-1.094 2.859,-2.894 V 0.808 c 0,-0.421 0.185,-0.572 0.572,-0.572 H 4.71 V -0.756 H 3.953 c -0.807,0 -1.346,0.353 -1.346,1.378 V 1.48 C 2.338,0.32 1.295,-0.925 -0.32,-0.925 c -1.328,0 -2.085,0.724 -2.085,1.8"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,503.8078,322.05213)"
       clip-path="url(#clipPath50)" />
    <path
       id="path51"
       d="m 0,0 c 0.471,0.066 0.689,0.167 0.689,0.604 v 0.22 H 0.067 V 2.809 H 1.396 V 0.756 c 0,-0.823 -0.269,-1.244 -1.144,-1.362 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,517.08017,325.13568)"
       clip-path="url(#clipPath52)" />
    <path
       id="path53"
       d="m 0,0 c 0,3.313 1.867,5.785 5.146,5.785 2.591,0 4.206,-1.463 4.862,-3.683 L 9.032,1.446 C 8.612,3.397 7.451,4.76 5.164,4.76 2.506,4.76 1.161,2.775 1.161,0 c 0,-2.775 1.345,-4.76 4.003,-4.76 2.287,0 3.448,1.362 3.868,3.314 l 0.976,-0.658 C 9.352,-4.322 7.737,-5.787 5.146,-5.787 1.867,-5.787 0,-3.313 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,534.39235,312.17495)"
       clip-path="url(#clipPath54)" />
    <path
       id="path55"
       d="m 0,0 v 11.269 h 1.11 v -5.45 c 0.286,1.312 1.245,2.423 2.894,2.423 1.564,0 2.607,-0.943 2.607,-2.794 L 6.611,0 H 5.5 V 5.197 C 5.5,6.509 4.929,7.315 3.549,7.315 1.817,7.315 1.11,6.071 1.11,4.676 V 0 Z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,557.66733,322.79008)"
       clip-path="url(#clipPath56)" />
    <path
       id="path57"
       d="m 361.307,167.56 h 1.11 v -8.073 h -1.11 z m 0,3.195 h 1.11 v -1.749 h -1.11 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,-39.918108,622.35695)"
       clip-path="url(#clipPath58)" />
    <path
       id="path59"
       d="M 0,0 V 8.073 H 1.11 V 5.819 c 0.286,1.312 1.211,2.423 2.826,2.423 1.598,0 2.675,-0.943 2.675,-2.794 V 0 H 5.5 V 5.197 C 5.5,6.509 4.895,7.315 3.499,7.315 1.8,7.315 1.11,6.071 1.11,4.676 V 0 Z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,584.46814,323.52717)"
       clip-path="url(#clipPath60)" />
    <path
       id="path61"
       d="M 0,0 H 5.181 C 5.08,1.815 4.088,2.74 2.607,2.74 1.127,2.74 0.151,1.815 0,0 m -1.077,-0.556 c 0,2.624 1.498,4.206 3.684,4.206 2.204,0 3.583,-1.481 3.583,-3.92 0,-0.201 -0.033,-0.404 -0.051,-0.538 h -6.156 c 0.068,-2.019 1.161,-3.028 2.759,-3.028 1.632,0 2.338,0.892 2.692,1.683 L 6.106,-2.944 C 5.534,-3.937 4.458,-4.761 2.742,-4.761 c -2.254,0 -3.819,1.531 -3.819,4.205"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,603.53001,314.56579)"
       clip-path="url(#clipPath62)" />
    <path
       id="path63"
       d="M 0,0 0.824,0.623 C 1.077,-0.472 2.086,-1.38 3.718,-1.38 4.794,-1.38 5.5,-0.942 5.5,-0.101 5.5,0.74 4.777,1.042 3.684,1.295 L 2.591,1.548 C 1.43,1.833 0.235,2.371 0.235,3.817 c 0,1.398 1.111,2.273 2.776,2.273 1.716,0 2.843,-0.859 3.247,-2.003 L 5.417,3.465 C 5.046,4.559 4.206,5.163 2.944,5.163 1.968,5.163 1.279,4.709 1.279,3.869 1.279,3.027 1.884,2.725 3.062,2.439 L 4.138,2.17 C 5.82,1.749 6.493,1.061 6.493,-0.084 6.493,-1.631 5.181,-2.321 3.684,-2.321 2.036,-2.321 0.488,-1.581 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,619.03764,319.32673)"
       clip-path="url(#clipPath64)" />
    <path
       id="path65"
       d="m 396.788,167.56 h 1.11 v -8.073 h -1.11 z m 0,3.195 h 1.11 v -1.749 h -1.11 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,-39.918108,622.35695)"
       clip-path="url(#clipPath66)" />
    <path
       id="path67"
       d="m 0,0 c 0,2.153 -1.076,3.296 -2.674,3.296 -1.581,0 -2.658,-1.143 -2.658,-3.296 0,-2.136 1.077,-3.264 2.658,-3.264 C -1.076,-3.264 0,-2.136 0,0 m -6.442,0 c 0,2.557 1.547,4.206 3.768,4.206 2.22,0 3.784,-1.649 3.784,-4.206 0,-2.557 -1.564,-4.205 -3.784,-4.205 -2.221,0 -3.768,1.648 -3.768,4.205"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,654.98096,315.65029)"
       clip-path="url(#clipPath68)" />
    <path
       id="path69"
       d="m 412.601,170.755 h 1.11 v -11.269 h -1.11 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.8783176,-39.918108,622.35695)"
       clip-path="url(#clipPath70)" />
    <path
       id="path71"
       d="m 0,0 c 0,2.153 -1.077,3.296 -2.675,3.296 -1.581,0 -2.658,-1.143 -2.658,-3.296 0,-2.136 1.077,-3.264 2.658,-3.264 C -1.077,-3.264 0,-2.136 0,0 m -6.443,0 c 0,2.557 1.548,4.206 3.768,4.206 2.221,0 3.785,-1.649 3.785,-4.206 0,-2.557 -1.564,-4.205 -3.785,-4.205 -2.22,0 -3.768,1.648 -3.768,4.205"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,681.92557,315.65029)"
       clip-path="url(#clipPath72)" />
    <path
       id="path73"
       d="m 0,0 c 0,1.228 -0.857,2.019 -2.069,2.019 -1.194,0 -2.052,-0.791 -2.052,-2.019 0,-1.228 0.858,-2.019 2.052,-2.019 C -0.857,-2.019 0,-1.228 0,0 m 0.689,-6.224 c 0,0.639 -0.454,0.924 -1.412,0.924 h -3.28 c -0.404,-0.251 -0.841,-0.722 -0.841,-1.243 0,-0.791 0.891,-1.245 2.422,-1.245 1.952,0 3.111,0.64 3.111,1.564 m -4.406,3.718 c -0.252,-0.304 -0.505,-0.723 -0.505,-1.111 0,-0.454 0.269,-0.723 1.144,-0.723 h 2.506 c 1.413,0 2.288,-0.571 2.288,-1.749 0,-1.497 -1.548,-2.574 -4.138,-2.574 -2.052,0 -3.33,0.69 -3.33,1.8 0,0.841 0.807,1.362 1.446,1.615 -0.555,0.151 -0.824,0.538 -0.824,1.025 0,0.657 0.505,1.329 1.177,1.851 -0.773,0.505 -1.244,1.346 -1.244,2.372 0,1.682 1.261,2.877 3.128,2.877 0.589,0 1.111,-0.119 1.565,-0.32 L 0.151,4.508 1.312,4.07 -0.134,2.338 C 0.623,1.816 1.06,0.992 1.06,0 c 0,-1.682 -1.245,-2.877 -3.129,-2.877 -0.622,0 -1.177,0.135 -1.648,0.371"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,698.75112,313.05643)"
       clip-path="url(#clipPath74)" />
    <path
       id="path75"
       d="m 0,0 c 0,2.153 -1.077,3.296 -2.675,3.296 -1.58,0 -2.657,-1.143 -2.657,-3.296 0,-2.136 1.077,-3.264 2.657,-3.264 C -1.077,-3.264 0,-2.136 0,0 m -6.442,0 c 0,2.557 1.547,4.206 3.767,4.206 2.221,0 3.785,-1.649 3.785,-4.206 0,-2.557 -1.564,-4.205 -3.785,-4.205 -2.22,0 -3.767,1.648 -3.767,4.205"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7038546,0,0,-1.9516038,717.03927,315.65029)"
       clip-path="url(#clipPath76)" />
    <path
       id="path77"
       d="M 0,0 V 32.287 H 21.686 V 29.491 H 3.181 V 17.88 H 18.553 V 15.035 H 3.181 V 2.796 H 21.686 V 0 Z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,206.1312,294.3403)"
       clip-path="url(#clipPath78)" />
    <path
       id="path79"
       d="m 0,0 v 23.132 h 3.181 v -6.458 c 0.819,3.807 3.373,6.94 7.662,6.94 4.192,0 6.65,-2.362 6.891,-7.037 0.771,3.808 3.421,7.037 7.759,7.037 4.433,0 6.939,-2.65 6.939,-7.951 V 0 h -3.181 v 14.938 c 0,4.048 -1.783,6.026 -5.349,6.026 -4.144,0 -6.12,-3.567 -6.12,-7.567 V 0 h -3.133 v 14.938 c 0,4.048 -1.782,6.026 -5.397,6.026 -4.095,0 -6.071,-3.567 -6.071,-7.567 L 3.181,0 Z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,245.48291,294.3403)"
       clip-path="url(#clipPath80)" />
    <path
       id="path81"
       d="m 0,0 c 4.289,0 7.469,3.326 7.469,6.459 v 4.096 L -0.049,6.988 C -2.554,5.832 -3.71,4.676 -3.71,2.941 -3.71,1.013 -2.265,0 0,0 m -6.891,2.508 c 0,3.035 2.265,4.963 5.494,6.408 l 8.77,4 v 0.192 c 0,3.471 -1.639,5.494 -5.397,5.494 -3.471,0 -5.976,-1.976 -7.181,-4.53 l -2.216,2.216 c 1.445,2.41 4.819,5.158 9.734,5.158 5.397,0 8.192,-3.133 8.192,-8.29 V 2.313 c 0,-1.204 0.53,-1.637 1.639,-1.637 h 1.349 v -2.844 h -2.169 c -2.313,0 -3.855,1.013 -3.855,3.951 V 4.242 C 6.698,0.916 3.71,-2.65 -0.916,-2.65 c -3.807,0 -5.975,2.074 -5.975,5.158"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,316.39661,290.54809)"
       clip-path="url(#clipPath82)" />
    <path
       id="path83"
       d="m 0,0 v 23.132 h 3.181 v -6.458 c 0.819,3.759 3.469,6.94 8.096,6.94 4.577,0 7.661,-2.699 7.661,-8 V 0 h -3.18 v 14.891 c 0,3.758 -1.735,6.073 -5.735,6.073 -4.866,0 -6.842,-3.567 -6.842,-7.567 L 3.181,0 Z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,343.12117,294.3403)"
       clip-path="url(#clipPath84)" />
    <path
       id="path85"
       d="M 0,0 V 15.566 H 3.132 V 0.675 c 0,-3.71 1.735,-6.024 5.687,-6.024 5.011,0 6.939,3.518 6.939,7.566 V 15.566 H 18.89 V -7.565 h -3.132 v 6.601 C 14.938,-4.818 12.336,-8.048 7.565,-8.048 3.036,-8.048 0,-5.349 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,381.32466,281.10653)"
       clip-path="url(#clipPath86)" />
    <path
       id="path87"
       d="M 0,0 H 14.842 C 14.553,5.204 11.709,7.854 7.469,7.854 3.228,7.854 0.434,5.204 0,0 m -3.084,-1.591 c 0,7.519 4.289,12.049 10.553,12.049 6.313,0 10.265,-4.242 10.265,-11.229 0,-0.579 -0.097,-1.156 -0.145,-1.542 H -0.048 c 0.192,-5.783 3.325,-8.675 7.902,-8.675 4.675,0 6.699,2.554 7.711,4.82 l 1.928,-2.266 c -1.639,-2.842 -4.723,-5.205 -9.639,-5.205 -6.457,0 -10.938,4.387 -10.938,12.048"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,422.97096,271.32683)"
       clip-path="url(#clipPath88)" />
    <path
       id="path89"
       d="m 292.81,210.784 h 3.181 v -32.287 h -3.181 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,-40.355519,606.57527)"
       clip-path="url(#clipPath90)" />
    <path
       id="path91"
       d="M 0,0 H 14.842 C 14.553,5.204 11.709,7.854 7.469,7.854 3.228,7.854 0.434,5.204 0,0 m -3.084,-1.591 c 0,7.519 4.289,12.049 10.553,12.049 6.313,0 10.265,-4.242 10.265,-11.229 0,-0.579 -0.097,-1.156 -0.145,-1.542 H -0.048 c 0.192,-5.783 3.325,-8.675 7.902,-8.675 4.675,0 6.699,2.554 7.711,4.82 l 1.928,-2.266 c -1.639,-2.842 -4.723,-5.205 -9.639,-5.205 -6.457,0 -10.938,4.387 -10.938,12.048"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,472.89699,271.32683)"
       clip-path="url(#clipPath92)" />
    <path
       id="path93"
       d="m 0,0 v -10.987 h 8.529 c 5.108,0 7.47,1.637 7.47,5.492 C 15.999,-1.591 12.866,0 8.674,0 Z M 0,13.879 V 3.758 h 8.192 c 3.711,0 6.361,1.158 6.361,5.061 0,3.855 -2.699,5.06 -6.554,5.06 z M -4.193,-14.698 V 17.589 H 9.445 c 6.168,0 9.445,-3.325 9.445,-8.239 0,-3.471 -2.265,-6.602 -7.662,-7.181 6.747,-0.724 9.107,-4.049 9.107,-8.193 0,-5.494 -4.144,-8.674 -11.131,-8.674 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,528.48028,268.62949)"
       clip-path="url(#clipPath94)" />
    <path
       id="path95"
       d="M 0,0 H 13.638 C 13.204,4.288 10.698,6.553 6.795,6.553 3.036,6.553 0.53,4.336 0,0 m -4.144,-2.071 c 0,7.372 4.529,12.142 10.939,12.142 6.601,0 10.601,-4.385 10.601,-11.227 0,-0.724 -0.096,-1.447 -0.192,-1.88 h -17.3 c 0.241,-4.915 2.988,-7.71 7.132,-7.71 4.674,0 6.553,2.555 7.469,4.577 l 2.554,-2.89 c -1.734,-2.795 -4.963,-5.158 -10.023,-5.158 -6.602,0 -11.18,4.723 -11.18,12.146"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,572.58452,270.3156)"
       clip-path="url(#clipPath96)" />
    <path
       id="path97"
       d="m 380.171,210.784 h 4.193 v -32.287 h -4.193 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,-40.355519,606.57527)"
       clip-path="url(#clipPath98)" />
    <path
       id="path99"
       d="m 387.784,210.784 h 4.193 v -32.287 h -4.193 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,-40.355519,606.57527)"
       clip-path="url(#clipPath100)" />
    <path
       id="path101"
       d="m 0,0 c 0,5.926 -2.795,8.624 -6.795,8.624 -3.952,0 -6.699,-2.698 -6.699,-8.624 0,-5.88 2.747,-8.628 6.699,-8.628 4,0 6.795,2.748 6.795,8.628 m -17.782,0 c 0,7.372 4.53,12.143 10.987,12.143 C -0.337,12.143 4.24,7.372 4.24,0 c 0,-7.374 -4.577,-12.146 -11.035,-12.146 -6.457,0 -10.987,4.772 -10.987,12.146"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,659.81281,273.93881)"
       clip-path="url(#clipPath102)" />
    <path
       id="path103"
       d="m 0,0 v 23.323 h 4.192 v -6.216 c 0.916,3.615 3.422,6.699 7.999,6.699 4.579,0 7.422,-2.746 7.422,-7.709 V 0 h -4.144 v 15.084 c 0,3.035 -1.205,5.155 -5.157,5.155 -4.481,0 -6.12,-3.132 -6.12,-7.036 L 4.192,0 Z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,671.04179,294.3403)"
       clip-path="url(#clipPath104)" />
    <path
       id="path105"
       d="m 441.078,201.82 h 4.192 v -23.323 h -4.192 z m 0,8.964 h 4.192 v -5.204 h -4.192 z"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.7013351,0,0,-1.7492489,-40.355519,606.57527)"
       clip-path="url(#clipPath106)" />
    <path
       id="path107"
       d="m 0,0 c 0,0 -14.061,-0.246 -19.816,6.929 0,0 4.652,8.753 4.967,9.698 0.316,0.946 4.81,8.674 5.441,10.565 0.631,1.893 1.498,2.603 2.996,2.76 1.498,0.158 19.475,6.781 19.475,6.781 0,0 5.362,0.158 5.756,0.158 0.394,0 3.311,-0.394 4.258,-0.631 0.946,-0.237 2.444,-0.63 2.444,-0.63 0,0 1.971,-1.262 2.129,-1.814 0.158,-0.552 -0.316,-4.021 -0.237,-4.416 0.079,-0.394 1.34,0.631 0.158,-3.468 C 26.388,21.831 26.467,19.781 25.915,19.309 25.363,18.835 15.428,7.244 15.428,7.244 c 0,0 -5.046,-4.257 -5.597,-4.257 C 9.278,2.987 0,0 0,0"
       style="fill:#00796b;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,125.18214,275.10272)"
       clip-path="url(#clipPath108)" />
    <path
       id="path109"
       d="m 0,0 c 0.039,6.948 1.428,13.603 4.161,19.98 0.149,0.35 0.315,0.693 0.478,1.036 0.457,0.968 1.235,1.257 2.203,0.921 C 7.693,21.64 8.016,20.695 7.643,19.698 6.963,17.879 6.166,16.1 5.57,14.254 c -8.267,-25.566 6.856,-53.044 32.861,-59.749 0.409,-0.106 0.825,-0.193 1.225,-0.328 0.882,-0.3 1.326,-0.944 1.152,-1.854 -0.186,-0.975 -0.877,-1.423 -1.86,-1.309 -0.542,0.061 -1.077,0.214 -1.607,0.356 C 16.771,-43.093 2.188,-25.756 0.275,-4.554 0.139,-3.04 0.09,-1.518 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,78.630695,271.38305)"
       clip-path="url(#clipPath110)" />
    <path
       id="path111"
       d="m 0,0 c -0.113,0.037 -0.217,0.094 -0.302,0.16 -0.49,0.359 -0.933,0.782 -1.301,1.264 -0.358,0.471 -0.641,0.98 -0.914,1.499 -0.557,1.056 -0.905,2.178 -1.104,3.356 -0.16,0.896 -0.235,1.801 -0.131,2.706 0.037,0.339 0.065,0.689 0.188,1.009 0.085,0.217 0.217,0.377 0.415,0.49 0.349,0.207 0.679,0.085 0.811,-0.292 C -2.3,10.088 -2.272,9.984 -2.263,9.871 -2.244,9.617 -2.225,9.353 -2.244,9.099 -2.282,8.617 -2.348,8.146 -2.395,7.665 -2.47,7.005 -2.451,6.354 -2.31,5.713 -2.178,5.091 -1.97,4.497 -1.763,3.903 -1.405,2.866 -0.952,1.886 -0.273,1.018 -0.169,0.877 -0.057,0.726 0.047,0.584 0.142,0.452 0.189,0.302 0.189,0.142 0.189,0.009 0.113,-0.048 0,0 M 6.355,0.971 C 6.515,1.734 6.478,2.498 6.327,3.262 6.091,4.469 5.61,5.581 4.997,6.638 4.639,7.25 4.252,7.835 3.8,8.391 2.885,9.522 1.848,10.55 0.585,11.323 c -0.698,0.434 -1.443,0.764 -2.282,0.849 -0.34,0.038 -0.669,0.018 -0.99,-0.067 -0.575,-0.15 -0.98,-0.518 -1.254,-1.027 C -4.158,10.663 -4.29,10.229 -4.384,9.777 -4.601,8.702 -4.582,7.627 -4.459,6.553 -4.271,4.894 -3.818,3.3 -3.074,1.801 c 0.218,-0.434 0.481,-0.84 0.736,-1.245 0.424,-0.698 0.971,-1.273 1.593,-1.791 0.906,-0.754 1.943,-1.113 3.121,-1.084 h 0.142 c 0.019,-0.077 0.037,-0.142 0.047,-0.208 0.094,-0.604 0.179,-1.207 0.254,-1.82 0.048,-0.509 0.095,-1.028 0.132,-1.537 0.038,-0.717 0.085,-1.442 0.123,-2.159 0.82,0.462 1.65,0.877 2.498,1.272 -0.094,0.557 -0.207,1.123 -0.32,1.679 -0.113,0.557 -0.236,1.122 -0.358,1.679 -0.114,0.537 -0.227,1.065 -0.34,1.602 -0.019,0.085 0,0.132 0.075,0.179 0.934,0.632 1.5,1.5 1.726,2.603"
       style="fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,113.95258,218.62091)"
       clip-path="url(#clipPath112)" />
    <path
       id="path113"
       d="m 0,0 c -0.17,0 -0.34,0.028 -0.491,0.075 -0.858,0.292 -1.659,0.669 -2.404,1.179 -0.707,0.49 -1.338,1.084 -1.952,1.678 -1.263,1.225 -2.262,2.63 -3.083,4.176 -0.622,1.189 -1.14,2.414 -1.414,3.735 -0.104,0.49 -0.226,0.98 -0.207,1.499 0.018,0.33 0.141,0.622 0.367,0.867 0.396,0.443 0.906,0.434 1.264,-0.047 0.094,-0.123 0.189,-0.255 0.254,-0.405 0.132,-0.35 0.283,-0.708 0.377,-1.066 0.17,-0.688 0.293,-1.386 0.444,-2.084 C -6.647,8.664 -6.327,7.76 -5.836,6.92 -5.365,6.119 -4.799,5.374 -4.243,4.639 -3.272,3.365 -2.188,2.196 -0.849,1.301 -0.632,1.141 -0.406,0.989 -0.189,0.829 0,0.697 0.123,0.499 0.198,0.282 0.264,0.104 0.188,-0.01 0,0 m 10.428,-6.072 c -0.245,0.452 -0.5,0.905 -0.755,1.357 -0.82,1.424 -1.65,2.848 -2.479,4.262 C 7.128,-0.34 7.128,-0.265 7.212,-0.161 8.231,1.141 8.627,2.621 8.438,4.252 8.306,5.393 7.91,6.449 7.354,7.438 6.477,9.022 5.289,10.361 3.95,11.559 c -0.782,0.688 -1.593,1.33 -2.47,1.895 -1.81,1.169 -3.715,2.121 -5.827,2.631 -1.188,0.282 -2.376,0.405 -3.592,0.142 -0.481,-0.105 -0.934,-0.284 -1.349,-0.547 -0.735,-0.472 -1.131,-1.17 -1.282,-2.009 -0.113,-0.669 -0.103,-1.348 -0.018,-2.018 0.188,-1.593 0.697,-3.092 1.367,-4.535 1.027,-2.244 2.376,-4.262 4.111,-6.006 0.499,-0.509 1.046,-0.952 1.593,-1.414 0.905,-0.763 1.942,-1.33 3.045,-1.763 1.613,-0.641 3.225,-0.669 4.856,-0.085 0.056,0.019 0.123,0.038 0.207,0.056 0.048,-0.094 0.104,-0.178 0.151,-0.263 0.764,-1.471 1.452,-2.98 2.065,-4.517 0.274,-0.679 0.547,-1.357 0.802,-2.046 0.452,-1.159 0.895,-2.319 1.32,-3.479 0.056,-0.141 0.103,-0.292 0.16,-0.434 0.952,1.227 1.971,2.395 3.045,3.498 -0.547,1.094 -1.112,2.179 -1.706,3.263"
       style="fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.3267534,89.384728,227.47288)"
       clip-path="url(#clipPath114)" />
    <path
       id="path115"
       d="m 0,0 c 0.16,0.764 0.123,1.527 -0.028,2.291 -0.236,1.207 -0.717,2.319 -1.33,3.376 -0.358,0.612 -0.745,1.197 -1.197,1.753 -0.915,1.132 -1.952,2.159 -3.215,2.933 -0.698,0.433 -1.443,0.763 -2.282,0.848 -0.34,0.038 -0.669,0.019 -0.99,-0.066 -0.575,-0.151 -0.98,-0.519 -1.254,-1.028 -0.217,-0.415 -0.349,-0.848 -0.443,-1.3 -0.217,-1.076 -0.198,-2.151 -0.075,-3.225 0.188,-1.659 0.641,-3.253 1.385,-4.752 0.218,-0.434 0.481,-0.84 0.736,-1.245 0.424,-0.697 0.971,-1.272 1.593,-1.791 0.906,-0.754 1.943,-1.112 3.121,-1.084 h 0.142 c 0.019,-0.076 0.037,-0.142 0.047,-0.208 0.094,-0.604 0.179,-1.207 0.254,-1.819 0.048,-0.51 0.095,-1.029 0.132,-1.537 0.038,-0.717 0.085,-1.443 0.123,-2.16 0.82,0.462 1.65,0.877 2.498,1.273 -0.094,0.556 -0.207,1.122 -0.32,1.679 -0.113,0.556 -0.236,1.122 -0.358,1.678 -0.114,0.537 -0.226,1.066 -0.34,1.603 -0.019,0.085 0,0.132 0.075,0.178 0.934,0.632 1.5,1.5 1.726,2.603"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,122.43162,217.42629)"
       clip-path="url(#clipPath116)" />
    <path
       id="path117"
       d="m 0,0 c -0.245,0.452 -0.5,0.905 -0.754,1.357 -0.821,1.424 -1.65,2.848 -2.48,4.262 -0.066,0.113 -0.066,0.189 0.019,0.292 1.018,1.302 1.414,2.782 1.226,4.413 -0.133,1.141 -0.529,2.197 -1.085,3.187 -0.877,1.584 -2.065,2.923 -3.404,4.12 -0.782,0.688 -1.593,1.33 -2.47,1.895 -1.81,1.169 -3.715,2.121 -5.826,2.631 -1.188,0.282 -2.376,0.405 -3.593,0.142 -0.481,-0.105 -0.933,-0.283 -1.348,-0.547 -0.736,-0.472 -1.132,-1.17 -1.283,-2.009 -0.112,-0.669 -0.103,-1.347 -0.018,-2.017 0.188,-1.594 0.698,-3.093 1.367,-4.536 1.027,-2.244 2.376,-4.261 4.111,-6.005 0.499,-0.51 1.046,-0.953 1.593,-1.414 0.905,-0.764 1.943,-1.331 3.046,-1.764 1.612,-0.641 3.224,-0.669 4.856,-0.085 0.056,0.019 0.122,0.038 0.207,0.057 0.047,-0.094 0.104,-0.179 0.15,-0.264 0.764,-1.471 1.453,-2.98 2.065,-4.517 0.274,-0.678 0.547,-1.357 0.802,-2.046 0.452,-1.159 0.896,-2.319 1.32,-3.479 0.057,-0.141 0.103,-0.292 0.16,-0.434 0.952,1.227 1.971,2.396 3.046,3.498 C 1.16,-2.169 0.594,-1.084 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.3267534,103.29768,235.52933)"
       clip-path="url(#clipPath118)" />
    <path
       id="path119"
       d="m 0,0 c -0.094,0.812 -0.377,1.622 -0.858,2.489 -0.688,1.245 -1.678,2.433 -2.998,3.612 -0.802,0.707 -1.537,1.273 -2.254,1.735 -1.876,1.216 -3.545,1.97 -5.242,2.376 -0.66,0.16 -1.235,0.235 -1.763,0.235 -0.349,0 -0.67,-0.028 -0.99,-0.103 -0.273,-0.057 -0.519,-0.152 -0.726,-0.293 -0.189,-0.113 -0.368,-0.282 -0.443,-0.736 -0.076,-0.433 -0.076,-0.914 -0.01,-1.479 0.151,-1.235 0.547,-2.527 1.207,-3.97 0.972,-2.102 2.225,-3.941 3.744,-5.469 0.357,-0.367 0.772,-0.716 1.206,-1.084 l 0.264,-0.216 c 0.679,-0.576 1.509,-1.047 2.527,-1.452 0.622,-0.246 1.216,-0.369 1.82,-0.369 0.254,0 0.518,0.029 0.782,0.077 0.038,0 0.067,0.008 0.094,0.008 l 0.095,0.029 c 0.5,0.123 1.735,0.5 2.583,1.414 l 0.123,0.161 c 0.075,0.094 0.151,0.197 0.235,0.32 0.057,0.075 0.104,0.16 0.18,0.302 C -0.028,-1.687 0.104,-0.904 0,0"
       style="fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,98.100188,220.63746)"
       clip-path="url(#clipPath120)" />
    <path
       id="path121"
       d="m 0,0 c -0.075,-0.142 -0.123,-0.227 -0.179,-0.302 -0.085,-0.123 -0.16,-0.226 -0.236,-0.32 l -0.123,-0.161 c -0.848,-0.914 -2.083,-1.291 -2.583,-1.414 l -0.094,-0.029 c -0.028,0 -0.056,-0.008 -0.095,-0.008 -0.263,-0.048 -0.527,-0.077 -0.782,-0.077 -0.603,0 -1.198,0.123 -1.82,0.369 -1.018,0.405 -1.848,0.877 -2.526,1.452 l -0.265,0.217 c -0.433,0.367 -0.848,0.716 -1.206,1.084 -1.518,1.527 -2.772,3.366 -3.743,5.468 -0.661,1.443 -1.056,2.735 -1.207,3.97 -0.066,0.565 -0.066,1.046 0.009,1.48 0.076,0.453 0.255,0.623 0.443,0.735 0.208,0.141 0.453,0.236 0.726,0.293 0.321,0.075 0.641,0.103 0.99,0.103 0.528,0 1.103,-0.075 1.763,-0.235 1.698,-0.406 3.366,-1.16 5.242,-2.376 0.717,-0.462 1.453,-1.028 2.254,-1.735 1.32,-1.179 2.31,-2.367 2.998,-3.612 C 0.047,4.035 0.33,3.225 0.424,2.413 0.528,1.509 0.396,0.726 0,0"
       style="fill:#00796b;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,97.534075,223.60719)"
       clip-path="url(#clipPath122)" />
    <path
       id="path123"
       d="m 0,0 c 0.049,-0.124 -0.002,-0.205 -0.134,-0.201 -0.119,-0.003 -0.238,0.014 -0.345,0.044 -0.606,0.19 -1.174,0.44 -1.705,0.785 -0.504,0.331 -0.956,0.736 -1.396,1.143 -0.906,0.836 -1.63,1.803 -2.232,2.873 -0.456,0.822 -0.84,1.671 -1.055,2.591 -0.081,0.342 -0.175,0.684 -0.171,1.047 0.008,0.232 0.089,0.439 0.243,0.614 0.27,0.318 0.627,0.32 0.886,-0.011 0.068,-0.084 0.137,-0.175 0.185,-0.28 0.099,-0.242 0.211,-0.491 0.283,-0.74 C -5.31,7.386 -5.212,6.899 -5.095,6.413 -4.94,5.756 -4.7,5.127 -4.342,4.548 -3.998,3.994 -3.589,3.481 -3.186,2.977 -2.484,2.101 -1.704,1.301 -0.751,0.695 -0.596,0.587 -0.435,0.485 -0.28,0.377 -0.146,0.287 -0.057,0.15 0,0"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,89.827691,223.27552)"
       clip-path="url(#clipPath124)" />
    <path
       id="path125"
       d="M 0,0 C 0.11,0.529 0.102,1.098 -0.02,1.744 -0.193,2.67 -0.567,3.623 -1.151,4.64 -1.507,5.251 -1.852,5.761 -2.206,6.2 -3.131,7.356 -4.023,8.18 -5.005,8.788 -5.387,9.026 -5.733,9.194 -6.065,9.305 -6.284,9.377 -6.492,9.427 -6.709,9.446 -6.893,9.468 -7.066,9.459 -7.226,9.414 -7.369,9.382 -7.517,9.313 -7.658,9.044 -7.796,8.787 -7.896,8.484 -7.973,8.115 -8.136,7.307 -8.157,6.412 -8.043,5.368 -7.871,3.843 -7.466,2.426 -6.831,1.148 -6.683,0.843 -6.495,0.537 -6.298,0.215 l 0.12,-0.192 c 0.307,-0.502 0.73,-0.972 1.286,-1.439 0.34,-0.284 0.688,-0.485 1.067,-0.611 0.16,-0.053 0.332,-0.091 0.508,-0.117 0.024,-0.007 0.043,-0.007 0.061,-0.013 l 0.065,-0.002 c 0.34,-0.028 1.195,-0.048 1.92,0.35 l 0.11,0.076 c 0.067,0.043 0.136,0.092 0.215,0.152 0.051,0.035 0.099,0.079 0.175,0.152 C -0.37,-1.056 -0.124,-0.591 0,0"
       style="fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,120.59185,217.16664)"
       clip-path="url(#clipPath126)" />
    <path
       id="path127"
       d="m 0,0 c -0.077,-0.073 -0.124,-0.117 -0.175,-0.152 -0.079,-0.06 -0.148,-0.11 -0.216,-0.153 l -0.11,-0.075 c -0.724,-0.398 -1.58,-0.378 -1.919,-0.35 l -0.065,0.001 c -0.018,0.006 -0.038,0.006 -0.062,0.014 -0.176,0.026 -0.348,0.064 -0.507,0.116 -0.38,0.126 -0.728,0.328 -1.068,0.612 -0.555,0.466 -0.979,0.936 -1.285,1.439 l -0.121,0.192 c -0.196,0.322 -0.384,0.627 -0.533,0.933 -0.635,1.277 -1.04,2.694 -1.211,4.22 -0.114,1.044 -0.094,1.938 0.069,2.747 0.077,0.369 0.177,0.672 0.315,0.929 0.142,0.268 0.29,0.338 0.432,0.37 0.16,0.045 0.334,0.053 0.518,0.032 0.217,-0.02 0.424,-0.069 0.644,-0.142 0.332,-0.11 0.677,-0.278 1.059,-0.516 0.982,-0.609 1.874,-1.432 2.8,-2.588 C -1.081,7.189 -0.737,6.68 -0.381,6.068 0.203,5.052 0.578,4.099 0.751,3.173 0.872,2.526 0.881,1.958 0.771,1.429 0.647,0.838 0.401,0.373 0,0"
       style="fill:#00796b;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,119.56381,218.92488)"
       clip-path="url(#clipPath128)" />
    <path
       id="path129"
       d="m 0,0 c 0.005,-0.089 -0.044,-0.129 -0.126,-0.099 -0.076,0.023 -0.147,0.058 -0.207,0.1 C -0.675,0.247 -0.98,0.522 -1.242,0.85 -1.489,1.163 -1.689,1.513 -1.881,1.859 -2.276,2.575 -2.53,3.334 -2.685,4.132 -2.8,4.743 -2.864,5.358 -2.807,5.981 c 0.02,0.232 0.033,0.466 0.111,0.694 0.053,0.144 0.147,0.258 0.281,0.336 0.236,0.142 0.46,0.069 0.555,-0.193 C -1.835,6.751 -1.811,6.68 -1.802,6.604 -1.791,6.431 -1.772,6.252 -1.779,6.08 -1.797,5.751 -1.837,5.425 -1.865,5.095 -1.904,4.649 -1.885,4.203 -1.781,3.765 -1.68,3.345 -1.529,2.938 -1.382,2.535 -1.123,1.839 -0.8,1.173 -0.327,0.594 -0.252,0.493 -0.172,0.396 -0.098,0.295 -0.032,0.211 -0.004,0.106 0,0"
       style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,114.79436,217.23272)"
       clip-path="url(#clipPath130)" />
    <path
       id="path131"
       d="m 0,0 c -23.53,0 -42.673,19.143 -42.673,42.673 0,23.53 19.143,42.673 42.673,42.673 8.551,0 16.802,-2.521 23.86,-7.288 0.863,-0.583 1.09,-1.755 0.507,-2.619 -0.583,-0.862 -1.755,-1.09 -2.618,-0.506 C 15.316,79.278 7.795,81.574 0,81.574 c -21.451,0 -38.902,-17.45 -38.902,-38.901 0,-21.45 17.451,-38.902 38.902,-38.902 9.503,0 18.654,3.464 25.765,9.754 0.78,0.69 1.971,0.618 2.661,-0.163 0.691,-0.78 0.617,-1.971 -0.163,-2.662 C 20.462,3.8 10.425,0 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,144.62294,324.09842)"
       clip-path="url(#clipPath132)" />
    <path
       id="path133"
       d="m 0,0 c -0.922,0.073 -2.328,0.137 -3.159,0.164 -0.292,0.01 -0.511,0.019 -0.63,0.027 0.201,0 0.411,0 0.63,-0.008 0.867,0 1.744,-0.037 2.62,-0.12 C -0.356,0.046 -0.183,0.027 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,149.71036,228.1381)"
       clip-path="url(#clipPath134)" />
    <path
       id="path135"
       d="m 0,0 c -0.441,0.814 -1.469,1.097 -2.26,0.622 -3.707,-2.204 -7.742,-3.798 -11.901,-4.407 -2.972,-0.419 -6.024,-0.328 -9.075,0.395 -0.881,0.214 -1.763,0.475 -2.633,0.769 -0.893,0.305 -1.775,0.655 -2.633,1.028 -12.341,5.357 -20.738,18.614 -19.427,32.073 0.056,0.644 0.147,1.289 0.248,1.933 0.712,4.249 2.306,8.386 4.577,12.115 2.894,4.712 6.894,8.747 11.629,11.493 7.968,4.623 17.936,5.64 26.683,2.736 -0.712,-4.962 -1.74,-9.923 -3.774,-14.5 -2.046,-4.577 -5.177,-8.771 -9.437,-11.426 -3.56,-2.227 -7.73,-3.232 -11.946,-3.322 -1.379,-0.034 -2.757,0.033 -4.136,0.191 -1.345,0.148 -2.679,0.385 -3.978,0.69 0,0 0.011,0.034 0.022,0.113 0.185,1.028 1.778,8.793 8.367,14.761 h -0.476 v 2.38 c -2.704,-1.583 -5.375,-3.778 -7.574,-6.869 -0.011,-0.01 -0.023,-0.033 -0.034,-0.055 -2.17,-3.279 -3.707,-6.985 -4.475,-10.839 -0.046,-0.226 -0.091,-0.441 -0.125,-0.666 -0.034,-0.227 -0.079,-0.452 -0.113,-0.69 -0.056,-0.362 -0.113,-0.734 -0.158,-1.119 2.373,-0.689 4.803,-1.231 7.255,-1.537 1.594,-0.215 3.199,-0.339 4.815,-0.339 0.52,0 1.028,0.012 1.548,0.034 4.589,0.204 9.188,1.402 13.098,3.842 6.047,3.764 10.002,10.205 12.353,16.93 1.277,3.64 2.136,7.425 2.577,11.268 0.135,1.209 0.214,2.531 -0.453,3.571 -0.779,1.209 -2.328,1.65 -3.74,1.932 -7.583,1.56 -15.743,1.718 -23.089,-1.005 -7.176,-2.667 -13.155,-8.058 -17.472,-14.286 -2.328,-3.356 -3.933,-7.12 -4.848,-11.063 -0.384,-1.685 -0.656,-3.402 -0.791,-5.131 -0.622,-7.572 1.141,-15.37 5.04,-21.846 3.899,-6.464 9.629,-11.448 16.534,-14.421 1.096,-0.462 2.226,-0.881 3.345,-1.22 4.238,-1.333 8.578,-1.752 12.952,-1.119 1.458,0.192 2.893,0.52 4.351,0.961 2.961,0.904 5.786,2.204 8.465,3.797 C 0.068,-1.773 0.429,-0.779 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,165.94781,306.50131)"
       clip-path="url(#clipPath136)" />
    <path
       id="path137"
       d="m 0,0 c 0.797,0.301 1.309,-0.824 0.57,-1.247 -10.329,-5.91 -12.54,-16.74 -12.758,-17.969 -0.011,-0.079 -0.022,-0.113 -0.022,-0.113 l -4.566,-2.983 c 0.045,0.383 0.101,0.756 0.158,1.119 0.034,0.237 0.079,0.463 0.113,0.689 0.034,0.226 0.079,0.441 0.124,0.666 0.87,4.465 2.475,8.025 4.475,10.839 0.012,0.022 0.023,0.045 0.034,0.056 C -8.496,-4.198 -4.003,-1.517 0,0"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,131.45461,245.31444)"
       clip-path="url(#clipPath138)" />
    <path
       id="path139"
       d="m 0,0 c -0.12,-0.229 -1.92,-3.399 -7.29,-4.27 -1.27,-0.2 -2.73,-0.28 -4.42,-0.16 -0.33,0.021 -0.66,0.05 -1.01,0.09 -9.78,1.09 -13.9,6.221 -13.9,6.221 l 0.83,3.819 c 4.92,-4.189 10.44,-5.819 15.11,-6.309 1.32,-0.141 2.57,-0.181 3.71,-0.181 3.98,0.03 6.76,0.74 6.97,0.79"
       style="fill:#2c2c2c;fill-opacity:1;fill-rule:nonzero;stroke:none"
       transform="matrix(1.3342305,0,0,-1.2306613,134.26638,269.14165)"
       clip-path="url(#clipPath140)" />
    <rect
       style="opacity:0;fill:#fff6d5;fill-opacity:0;fill-rule:evenodd;stroke:#000000;stroke-width:0"
       id="rect716"
       width="587.97919"
       height="94.190842"
       x="68.502434"
       y="356.78351" />
    <text
       xml:space="preserve"
       id="text716"
       style="font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;font-size:12px;font-family:sans-serif;-inkscape-font-specification:'sans-serif, Bold';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal;text-align:start;writing-mode:lr-tb;direction:ltr;white-space:pre;shape-inside:url(#rect717);opacity:0;fill:#fff6d5;fill-opacity:0;fill-rule:evenodd;stroke:#000000;stroke-width:0"><tspan
         x="68.501953"
         y="362.02218"
         id="tspan1">Dott.</tspan></text>
  </g>
</svg>
