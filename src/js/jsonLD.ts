import { type CollectionEntry } from "astro:content";
import { getImage } from "astro:assets";

// data
import siteData from "@/config/siteData.json.ts";
import { type LocationData, getLocationBusinessSchema } from "@config/locations";

interface GeneralProps {
	type: "general";
	location?: LocationData;
}

export interface BlogProps {
	type: "blog";
	postFrontmatter: CollectionEntry<"blog">["data"];
	image: Awaited<ReturnType<typeof getImage>>; // result of getImage() from Seo.astro
	authors: CollectionEntry<"authors">[];
	canonicalUrl: URL;
	location?: LocationData;
}

export interface CategoryProps {
	type: "category";
	category: string;
	postCount: number;
	canonicalUrl: URL;
	location?: LocationData;
}

export type JsonLDProps = BlogProps | GeneralProps | CategoryProps;

export default function jsonLDGenerator(props: JsonLDProps) {
	const { type } = props;
	if (type === "blog") {
		const { postFrontmatter, image, authors, canonicalUrl, location } = props as BlogProps;

		const authorsJsonLdArray = authors.map((author) => {
			return {
				"@type": "Person",
				name: author.data.name,
				url: author.data.authorLink,
			};
		});

		let authorsJsonLd: any;

		if (authorsJsonLdArray.length === 1) {
			authorsJsonLd = authorsJsonLdArray[0];
		} else {
			authorsJsonLd = authorsJsonLdArray;
		}

		// Location-specific publisher info
		const publisherInfo = location ? getLocationBusinessSchema(location) : {
			"@type": "Person",
			"name": "Dott. Emanuele Belloni"
		};

		return `<script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "mainEntityOfPage": {
          "@type": "WebPage",
          "@id": "${canonicalUrl}"
        },
        "headline": "${postFrontmatter.title}",
        "description": "${postFrontmatter.description}",
        "image": {
          "@type": "ImageObject",
          "url": "${image.src}",
          "width": ${image.attributes.width},
          "height": ${image.attributes.height}
        },
        "author": ${JSON.stringify(authorsJsonLd)},
        "publisher": ${JSON.stringify({
          ...publisherInfo,
          "url": import.meta.env.SITE,
          "sameAs": [
            "https://www.linkedin.com/in/emanuele-belloni/",
            "https://www.instagram.com/dott.emanuelebelloni/"
          ]
        })},
        "datePublished": "${postFrontmatter.pubDate}",
        "dateModified": "${postFrontmatter.updatedDate || postFrontmatter.pubDate}",
        "inLanguage": "it-IT",
        "keywords": "${postFrontmatter.categories?.join(', ')}"
      }
    </script>`;
	}

	if (type === "category") {
		const { category, postCount, canonicalUrl, location } = props as CategoryProps;

		// Funzione helper per humanize (semplificata)
		const humanize = (text: string) => {
			return text.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
		};

		const categoryDisplay = humanize(category);
		const locationDisplay = location ? ` - ${location.city}` : '';

		return `<script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "${categoryDisplay} - Dott. Emanuele Belloni${locationDisplay}",
        "description": "Scopri ${postCount} articoli su ${categoryDisplay.toLowerCase()} del Dott. Emanuele Belloni, Biologo nutrizionista e Chinesiologo${location ? ` a ${location.city}` : ''}.",
        "url": "${canonicalUrl}",
        "inLanguage": "it-IT",
        "isPartOf": {
          "@type": "WebSite",
          "name": "Dott. Emanuele Belloni - Biologo Nutrizionista",
          "url": "${import.meta.env.SITE}"
        },
        "breadcrumb": {
          "@type": "BreadcrumbList",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "Home",
              "item": "${import.meta.env.SITE}"
            },
            {
              "@type": "ListItem",
              "position": 2,
              "name": "Blog",
              "item": "${import.meta.env.SITE}/blog"
            },
            {
              "@type": "ListItem",
              "position": 3,
              "name": "${categoryDisplay}",
              "item": "${canonicalUrl}"
            }
          ]
        },
        "mainEntity": {
          "@type": "ItemList",
          "numberOfItems": ${postCount}
        }
      }
    </script>`;
	}

	// For general pages, use location if provided
	const { location } = props as GeneralProps;
	const businessSchema = location ? getLocationBusinessSchema(location) : null;

	return `<script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "${siteData.name}",
        "url": "${import.meta.env.SITE}",
        "description": "${siteData.description}",
        "inLanguage": "it-IT",
        "potentialAction": {
          "@type": "SearchAction",
          "target": {
            "@type": "EntryPoint",
            "urlTemplate": "${import.meta.env.SITE}/blog?q={search_term_string}"
          },
          "query-input": "required name=search_term_string"
        },
        "publisher": ${JSON.stringify(businessSchema || {
          "@type": "Person",
          "name": "Dott. Emanuele Belloni",
          "jobTitle": "Biologo Nutrizionista e Chinesiologo",
          "address": {
            "@type": "PostalAddress",
            "streetAddress": siteData.contact.address1,
            "addressLocality": "Bastia Umbra",
            "addressRegion": "Umbria",
            "postalCode": "06083",
            "addressCountry": "IT"
          },
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": siteData.contact.phone,
            "email": siteData.contact.email,
            "contactType": "customer service",
            "availableLanguage": "Italian"
          }
        })}
      }
    </script>`;
}
