---
/**
 * * Centered services page layout. This has a title, description, main text, and an image if desired
 * The CTA and any other sections can be added in here so that all services pages have them
 */
import { Image } from "astro:assets";

// main layout
import BaseLayout from "@layouts/BaseLayout.astro";

// components
import CtaCardSplit from "@components/Cta/CtaCardSplit.astro";
import Badge from "@components/Badge/Badge.astro";
import FaqAccordionsService from "@components/Faq/FaqAccordionsService.astro";

// FAQ data imports
import { faqDataBenessereIntestinale } from "@config/faqData/faqDataBenessereIntestinale.json.ts";
import { faqDataSaluteMetabolica } from "@config/faqData/faqDataSaluteMetabolica.json.ts";
import { faqDataRicomposizioneCorporea } from "@config/faqData/faqDataRicomposizioneCorporea.json.ts";
import { faqDataNutrizioneSportiva } from "@config/faqData/faqDataNutrizioneSportiva.json.ts";
import { faqDataNutrizioneMenopausa } from "@config/faqData/faqDataNutrizioneMenopausa.json.ts";
import { faqDataForzaVitalita } from "@config/faqData/faqDataForzaVitalita.json.ts";
import { faqDataNutrizioneGeriatrica } from "@config/faqData/faqDataNutrizioneGeriatrica.json.ts";

interface Props {
	title: string;
	description: string;
	image?: ImageMetadata; // an imported image
	serviceSlug?: string; // service slug to determine which FAQ data to use
}

const { title, description, image = undefined, serviceSlug = "" } = Astro.props as Props;

// Map service slugs to their corresponding FAQ data
const faqDataMap = {
	"benessere-intestinale": faqDataBenessereIntestinale,
	"salute-metabolica-e-cardiovascolare": faqDataSaluteMetabolica,
	"ricomposizione-corporea-e-gestione-del-peso": faqDataRicomposizioneCorporea,
	"nutrizione-sportiva-over-40": faqDataNutrizioneSportiva,
	"nutrizione-e-menopausa": faqDataNutrizioneMenopausa,
	"forza-e-vitalita-over-60": faqDataForzaVitalita,
	"nutrizione-e-periodo-geriatrico": faqDataNutrizioneGeriatrica,
};

// Get the appropriate FAQ data for this service
const serviceFaqData = faqDataMap[serviceSlug as keyof typeof faqDataMap] || [];
---

<BaseLayout title={title} description={description}>
	<!-- service heading -->
	<section class="site-container">
		<div
			class="overflow-x-clip bg-[url('/assets/pattern-light.svg')] bg-top bg-no-repeat pt-24 md:pt-32 dark:bg-[url('/assets/pattern-dark-big.svg')]"
		>
			<div class="mx-auto flex max-w-[950px] flex-col justify-center">
				<div class="mx-auto">
					<Badge>📌 Servizi</Badge>
				</div>
				<h1 class="h1 text-center mb-20">
					{title}
				</h1>
			</div>

			<!-- service main image -->
			{
				image && (
					<div class="mt-8 flex pb-8 md:mt-8">
						<div class="w-full flex justify-center">
							<div class="max-w-[800px] overflow-hidden px-4">
								<Image
									src={image}
									alt=""
									width={1600}
									quality="high"
									class="max-h-[45vh] rounded-md object-cover"
									loading="eager"
								/>
							</div>
						</div>
					</div>
				)
			}
		</div>
	</section>

	<!-- service details -->
	<section class="my-0">
		<div class="markdown-content mx-auto max-w-[800px] px-4">
			<slot />
		</div>
	</section>

	<!-- FAQ section - only show if FAQ data exists for this service -->
	{serviceFaqData.length > 0 && (
		<FaqAccordionsService faqData={serviceFaqData} />
	)}

	<CtaCardSplit />
</BaseLayout>