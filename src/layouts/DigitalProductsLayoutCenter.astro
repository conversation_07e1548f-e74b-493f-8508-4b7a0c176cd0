---
/**
 * * Centered digital products page layout for e-books, guides, and digital resources
 * Optimized for national audience with online targeting
 * Includes product showcase, description, and purchase/download CTAs
 */
import { Image } from "astro:assets";

// main layout
import BaseLayout from "@layouts/BaseLayout.astro";

// location configuration for national targeting
import { LOCATIONS } from "@config/locations.ts";

// components
import CtaCardSplit from "@components/Cta/CtaCardSplit.astro";
import Badge from "@components/Badge/Badge.astro";

interface Props {
	title: string;
	description: string;
	image?: ImageMetadata; // product cover image
	price?: string; // optional price display
	productType?: string; // e.g., "E-book", "Guida", "Corso"
}

const { 
	title, 
	description, 
	image = undefined, 
	price = undefined,
	productType = "Risorsa Digitale"
} = Astro.props as Props;

// Use online location for national SEO targeting
const onlineLocation = LOCATIONS.online;
---

<BaseLayout 
	title={title} 
	description={description} 
	image={image}
	primaryLocation={onlineLocation}
	type="general"
>
	<!-- Digital Product Hero Section -->
	<section class="site-container">
		<div
			class="overflow-x-clip bg-[url('/assets/pattern-light-big.svg')] bg-top bg-no-repeat pt-24 md:pt-32 dark:bg-[url('/assets/pattern-dark-big.svg')]"
		>
			<div class="mx-auto flex max-w-[950px] flex-col justify-center">
				<div class="mx-auto">
					<Badge>📚 {productType}</Badge>
				</div>
				<h1 class="h1 text-center mb-6">
					{title}
				</h1>
				
				<!-- Price display if provided -->
				{price && (
					<div class="text-center mb-8">
						<span class="text-2xl font-bold text-primary-600 dark:text-primary-400">
							{price}
						</span>
					</div>
				)}
				
				<!-- National availability indicator -->
				<div class="text-center text-base-600 dark:text-base-400 mb-8">
					<p class="text-lg">
						📍 Disponibile in tutta Italia • 💻 Accesso immediato • 🎯 Consulenza online inclusa
					</p>
				</div>
			</div>

			<!-- Product main image/cover -->
			{
				image && (
					<div class="mt-8 flex pb-8 md:mt-8">
						<div class="w-full flex justify-center">
							<div class="max-w-[600px] overflow-hidden px-4">
								<Image
									src={image}
									alt={`Copertina ${title}`}
									width={1200}
									height={630}
									quality="high"
									class="max-h-[50vh] rounded-lg object-cover shadow-xl"
									loading="eager"
								/>
							</div>
						</div>
					</div>
				)
			}
		</div>
	</section>

	<!-- Product Details and Content -->
	<section class="my-20">
		<div class="mx-auto max-w-[900px] px-4">
			<!-- Product description and features -->
			<div class="markdown-content">
				<slot />
			</div>
			
			<!-- Digital Product Features -->
			<div class="mt-16 grid gap-6 md:grid-cols-3">
				<div class="text-center p-6 bg-base-50 dark:bg-base-800 rounded-lg">
					<div class="text-3xl mb-4">📱</div>
					<h3 class="font-semibold mb-2">Accesso Multi-Device</h3>
					<p class="text-sm text-base-600 dark:text-base-400">
						Disponibile su smartphone, tablet e computer
					</p>
				</div>
				
				<div class="text-center p-6 bg-base-50 dark:bg-base-800 rounded-lg">
					<div class="text-3xl mb-4">🎯</div>
					<h3 class="font-semibold mb-2">Consulenza Inclusa</h3>
					<p class="text-sm text-base-600 dark:text-base-400">
						Supporto online personalizzato del Dott. Belloni
					</p>
				</div>
				
				<div class="text-center p-6 bg-base-50 dark:bg-base-800 rounded-lg">
					<div class="text-3xl mb-4">🔄</div>
					<h3 class="font-semibold mb-2">Aggiornamenti Gratuiti</h3>
					<p class="text-sm text-base-600 dark:text-base-400">
						Ricevi automaticamente le nuove versioni
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section for Digital Products -->
	<section class="py-16 bg-primary-50 dark:bg-primary-900/20">
		<div class="site-container text-center">
			<h2 class="h2 mb-6">Inizia il Tuo Percorso Oggi</h2>
			<p class="text-lg mb-8 max-w-2xl mx-auto text-base-600 dark:text-base-400">
				Accedi immediatamente al contenuto e inizia a trasformare la tua alimentazione con il supporto professionale del Dott. Emanuele Belloni.
			</p>
			
			<div class="flex flex-col sm:flex-row gap-4 justify-center">
				<a 
					href="/contatti-e-prenotazioni" 
					class="btn btn-primary"
				>
					Acquista Ora
				</a>
				<a 
					href="/contatti-e-prenotazioni" 
					class="btn btn-outline"
				>
					Richiedi Info
				</a>
			</div>
		</div>
	</section>

	<!-- Standard CTA -->
	<CtaCardSplit />
</BaseLayout>
