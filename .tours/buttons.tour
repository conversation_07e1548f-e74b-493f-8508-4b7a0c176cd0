{"$schema": "https://aka.ms/codetour-schema", "title": "buttons", "steps": [{"file": "src/components/Button/Button.astro", "description": "This Button component works for both links (<a>anchor tags</a>) and buttons (<button>a button</button>). See the Notes above this for some further details.", "line": 10}, {"file": "src/styles/buttons.css", "description": "You can edit button styling in this `buttons.css` file. I have defined normal, hover, focus, and active effects. \r\n\r\nIf you have more complex edits involving `group` and `group-hover`, you may need to edit the `Button.astro` component.", "line": 2}, {"file": "src/components/Forms/ContactForm.astro", "description": "This is an example of the component being used as a for submitting a form.", "line": 159}, {"file": "src/components/Hero/HeroSideImage.astro", "description": "This is an example of the component being used as an anchor tag <a>", "line": 71}]}