{"$schema": "https://aka.ms/codetour-schema", "title": "changing components on pages", "steps": [{"file": "src/pages/index.astro", "description": "Lets say we want to change the hero section on the homepage (src/pages/index.astro file). There are already 3 components available under src/components/hero. So for example, you can change this \"@components/Hero/HeroSideImage.astro\" to \"@components/Hero/HeroCentered.astro\" and your Hero section will now be a different already crafted option.", "line": 6}, {"file": "src/components/Hero/HeroSideImage.astro", "description": "You can also go into a component itself, here \"HeroSideImage.astro\", and edit anything you want. For instance, on this line you could change the image you are using to another one you have downloaded.\r\n\r\nYou can of course edit any styling to your liking.", "line": 19}]}